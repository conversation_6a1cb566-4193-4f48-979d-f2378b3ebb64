# 运维工具配置示例

## 工具参数配置

如果SFile.exe或Themida.exe的命令行参数与脚本中的默认设置不同，您可能需要修改脚本。

### SFile.exe参数配置

当前脚本中的调用方式：
```python
subprocess.run([str(sfile_exe), str(data_folder)], ...)
```

如果您的SFile.exe需要不同的参数，请修改`运维GUI工具.py`中的`pack_data_folder`方法。

常见的参数格式：
- `SFile.exe input_folder` (当前使用)
- `SFile.exe -pack input_folder output_file`
- `SFile.exe /input:folder /output:file`

### Themida.exe参数配置

当前脚本中的调用方式：
```python
subprocess.run([str(themida_exe), str(game_exe), str(game_protected)], ...)
```

如果您的Themida.exe需要不同的参数，请修改`运维GUI工具.py`中的`protect_game_exe`方法。

常见的参数格式：
- `Themida.exe input.exe output.exe` (当前使用)
- `Themida.exe -protect input.exe -output output.exe`
- `Themida.exe /input:input.exe /output:output.exe`

## launcher.shaiya配置文件示例

```ini
[Patch]
Version=0x010000d5
0x010000d1=a1b2c3d4e5f6789012345678901234567
0x010000d2=b2c3d4e5f6789012345678901234567a1
0x010000d3=c3d4e5f6789012345678901234567a1b2
0x010000d4=d4e5f6789012345678901234567a1b2c3
0x010000d5=e5f6789012345678901234567a1b2c3d4

[Other]
SomeOtherConfig=value
```

工具会自动：
1. 将Version值从`0x010000d5`更新为`0x010000d6`
2. 添加新行：`0x010000d6=新计算的MD5值`

## 文件夹结构示例

### 数据文件夹结构
```
C:\维护数据\
├── 运营包_v1.2.3.zip
│   └── (解压后)
│       └── SomeFolder\
│           └── Client\
│               └── data\
│                   ├── file1.dat
│                   ├── file2.dat
│                   └── ...
├── 技术包_v1.2.3.rar
│   └── (解压后)
│       └── GameFiles\
│           └── client\
│               ├── game.exe
│               └── other_files...
└── 其他文件夹...
```

### 工作目录结构
```
工作目录\
├── 运维GUI工具.py
├── SFile.exe
├── Themida.exe
├── launcher.shaiya
├── patch~\                    # 自动创建
│   ├── update.saf            # SFile.exe生成
│   ├── update.sah            # SFile.exe生成
│   └── game.exe              # 签名后的文件
├── patch~.zip                # 自动创建
└── [MD5值].bin              # 最终文件
```

## 常见问题解决

### 1. 编码问题
如果launcher.shaiya文件使用GBK编码，修改脚本中的编码设置：
```python
# 将
with open(launcher_config, 'r', encoding='utf-8') as f:
# 改为
with open(launcher_config, 'r', encoding='gbk') as f:
```

### 2. 路径问题
如果工具不在当前目录，可以修改脚本中的路径：
```python
# 在__init__方法中添加
self.sfile_path = Path("C:/Tools/SFile.exe")  # 自定义路径
self.themida_path = Path("C:/Tools/Themida.exe")  # 自定义路径
```

### 3. 权限问题
确保脚本有权限：
- 读取数据文件夹
- 写入工作目录
- 执行SFile.exe和Themida.exe

### 4. RAR文件处理
如果无法处理RAR文件，请确保：
1. 已安装rarfile包：`pip install rarfile`
2. 系统中有WinRAR或7-Zip
3. 或者手动解压RAR文件后使用文件夹模式

## 自定义配置

您可以在脚本开头修改默认配置：

```python
class MaintenanceGUI:
    def __init__(self, root):
        # 自定义默认路径
        self.default_data_path = r"D:\MyData"  # 修改默认数据路径
        
        # 自定义工具路径
        self.sfile_exe = Path("C:/Tools/SFile.exe")
        self.themida_exe = Path("C:/Tools/Themida.exe")
        
        # 其他配置...
```
