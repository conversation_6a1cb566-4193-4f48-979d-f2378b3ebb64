<?xml version="1.0" encoding="UTF-8"?>
<BRAWL_MAIN>
	<BRAWL AgainTime = "20" EndTime = "21" UnbeatableBuffID	= "809" KillMultiple = "1">
		<!-- 活动周几开启（大乱斗和魔龙都是这里控制） -->
		<BRAWL_WEEK>(2,4)</BRAWL_WEEK>
		<!-- 战二场景在没有活动时，周几能进入 -->
		<ENTER_WEEK_CNT>2</ENTER_WEEK_CNT>
		<ENTER_WEEK>(0,6)</ENTER_WEEK>
		<!-- 魔龙配置 -->
		<DRAGON IntervalTime = "600000" BOSSRefreshHour = "20" BOSSRefreshMinute= "30">
			<DRAGON_BOSS>
				<!-- BOSS配置 -->
				<BOSS_ID>1973</BOSS_ID>
				<!-- 刷怪坐标 -->
				<BOSS_POS_CNT>1</BOSS_POS_CNT>
				<BOSS_POS>(561-50-536)</BOSS_POS>
				<BOSS_HONOR_VALUE>1500</BOSS_HONOR_VALUE>
			</DRAGON_BOSS>
			<DRAGON_MOB>
				<!-- 怪物配置 -->
				<DRAGON_MOB_NUM>1</DRAGON_MOB_NUM>
				<DRAGON_MOB>(2752)</DRAGON_MOB>
				<MOB_HONOR_VALUE>(10)</MOB_HONOR_VALUE>
				<DRAGON_MOB_CNT>10</DRAGON_MOB_CNT>
				<!-- 刷怪坐标 -->
				<DRAGON_POS_NUM>6</DRAGON_POS_NUM>
				<MONSTER_POS>(642-50-585, 469-50-419, 545-50-352, 579-50-649, 679-50-479, 464-50-592)</MONSTER_POS>
			</DRAGON_MOB>
		</DRAGON>
		<!-- 前三个为白人防御塔和守护BOSS后三个为黑人 塔信息和怪物刷新一致 -->
		<!-- 塔信息[需按顺序击杀，推到第一个塔之后打开第二个塔的无敌] -->
		<PAGODE_INFO>
			<!-- 塔ID -->
			<PRGODE_ID>(181, 182, 183, 184, 185, 186)</PRGODE_ID>
			<!-- 塔位置 -->
			<PRGODE_POS>(532-50-544, 462-50-420, 447-50-309, 491-50-594, 580-50-658, 620-50-723)</PRGODE_POS>
		</PAGODE_INFO>
		<!-- 怪物[对应击杀之后刷新的怪物,那方先推掉塔先刷开始的怪物] -->
		<BRAWL_MOB>
			<MONSTER Time = "600000">
				<!-- 怪物ID -->
				<MONSTER_ID>(187, 188)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(532-50-544)</MONSTER_POS>
			</MONSTER>
			
			<MONSTER Time = "900000">
				<!-- 怪物ID -->
				<MONSTER_ID>(189, 190)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(532-50-544)</MONSTER_POS>
			</MONSTER>
			
			<MONSTER Time = "1200000">
				<!-- 怪物ID -->
				<MONSTER_ID>(191, 192)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(532-50-544)</MONSTER_POS>
			</MONSTER>
			<MONSTER Time = "600000">
				<!-- 怪物ID -->
				<MONSTER_ID>(193, 194)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(491-50-594)</MONSTER_POS>
			</MONSTER>
			
			<MONSTER Time = "900000">
				<!-- 怪物ID -->
				<MONSTER_ID>(195, 196)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(491-50-594)</MONSTER_POS>
			</MONSTER>
			
			<MONSTER Time = "1200000">
				<!-- 怪物ID -->
				<MONSTER_ID>(197, 198)</MONSTER_ID>
				<!-- 刷怪坐标 随机一个 -->
				<MONSTER_POS>(491-50-594)</MONSTER_POS>
			</MONSTER>
		</BRAWL_MOB>
		
		<BRAWL_RANK_WIN_PRIZE>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>1</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119113,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,3,4)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>3</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119114,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,2,3)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>10</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119115,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,1,2)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>50</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119116,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,20,5,20,20)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>100</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119117,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,20,5,10,10)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>300</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119118,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,10,5,5,5)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>10000</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119101,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,10,5,2,2)</ITEMNUM>
			</INTERGAL_REWARD>
		</BRAWL_RANK_WIN_PRIZE>
		
		<BRAWL_RANK_LOSE_PRIZE>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>1</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119113,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,2,3)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>3</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119114,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,1,2)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>10</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119115,78201,78207,41125,25177)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,30,5,1,1)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>50</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119116,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,20,5,15,15)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>100</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119117,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,20,5,8,8)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>300</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119118,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,10,5,4,4)</ITEMNUM>
			</INTERGAL_REWARD>
			<INTERGAL_REWARD>
				<!-- 排名 -->
				<RANK>10000</RANK>
				<!-- 奖励数据种类数量 -->
				<ITEMCNT>5</ITEMCNT>
				<!-- 奖励道具 -->
				<ITEMID>(119101,78201,78207,95011,95012)</ITEMID>
				<!-- 奖励道具对应的个数 -->
				<ITEMNUM>(1,10,5,2,2)</ITEMNUM>
			</INTERGAL_REWARD>
		</BRAWL_RANK_LOSE_PRIZE>
	</BRAWL>
</BRAWL_MAIN>