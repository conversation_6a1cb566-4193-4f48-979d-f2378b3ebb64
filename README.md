# 运维流程自动化工具

一个专业的图形化运维流程自动化工具，用于处理游戏客户端的运营包和技术包，自动化签名前后的各种操作。

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
双击运行 `启动工具.bat`，脚本会自动检查环境并启动工具。

### 方法二：手动启动
```bash
# 1. 安装依赖
python 安装依赖.py

# 2. 启动工具
python 运维GUI工具.py
```

## 📁 文件说明

| 文件名 | 类型 | 说明 |
|--------|------|------|
| `运维GUI工具.py` | 主程序 | 图形化界面的主工具 |
| `启动工具.bat` | 启动脚本 | 一键启动工具（Windows） |
| `安装依赖.py` | 安装脚本 | 自动安装所需依赖 |
| `测试工具.py` | 测试脚本 | 环境和配置测试 |
| `签名前操作.py` | 命令行工具 | 签名前处理（命令行版本） |
| `签名后操作.py` | 命令行工具 | 签名后处理（命令行版本） |
| `脚本使用说明.md` | 文档 | 详细使用说明 |
| `配置示例.md` | 文档 | 配置和自定义说明 |

## ✨ 主要功能

### 🎯 签名前处理
- ✅ 自动识别和解压zip/rar文件
- ✅ 智能查找Client/client文件夹
- ✅ 自动提取data文件夹并使用SFile.exe打包
- ✅ 自动提取game.exe并使用Themida.exe加壳
- ✅ 创建patch~文件夹并整理文件

### 🎯 签名后处理
- ✅ 处理SP小组返回的签名文件
- ✅ 自动创建zip压缩包
- ✅ 计算MD5值
- ✅ 自动更新launcher.shaiya配置文件
- ✅ 生成最终的.bin文件

### 🎯 用户界面
- ✅ 直观的图形化界面
- ✅ 实时日志输出
- ✅ 进度状态显示
- ✅ 错误提示和处理
- ✅ 支持多选文件夹（Ctrl+点击）

## 🛠️ 环境要求

### 必需环境
- Python 3.6 或更高版本
- Windows操作系统（推荐）

### 必需文件
- `SFile.exe` - 数据打包工具
- `Themida.exe` - 游戏加壳工具  
- `launcher.shaiya` - 配置文件

### 可选依赖
- `rarfile` - RAR文件支持（自动安装）
- WinRAR 或 7-Zip - RAR文件解压支持

## 📖 使用流程

### 1️⃣ 准备工作
1. 确保所有必需文件在工作目录下
2. 运行 `测试工具.py` 检查环境
3. 准备包含运营和技术文件的数据文件夹

### 2️⃣ 签名前处理
1. 启动图形化工具
2. 选择数据文件夹
3. 选择要处理的运营和技术文件夹
4. 点击"签名前处理"
5. 等待处理完成

### 3️⃣ 手动操作
1. 将处理后的game.exe发送给SP小组
2. 等待SP小组返回签名文件

### 4️⃣ 签名后处理
1. 点击"签名后处理"
2. 选择签名后的文件
3. 等待自动处理完成

## 🔧 配置说明

### 默认路径配置
```python
# 可在脚本中修改
self.default_data_path = r"C:\维护数据"  # 默认数据文件夹
```

### 工具参数配置
如果SFile.exe或Themida.exe的参数不同，请参考 `配置示例.md` 进行修改。

### 编码配置
如果launcher.shaiya使用GBK编码，请修改脚本中的编码设置。

## 🐛 故障排除

### 常见问题

1. **Python环境问题**
   - 确保安装了Python 3.6+
   - 运行 `python --version` 检查版本

2. **依赖包问题**
   - 运行 `python 安装依赖.py`
   - 手动安装：`pip install rarfile`

3. **工具执行问题**
   - 确保SFile.exe和Themida.exe在当前目录
   - 检查文件权限和路径

4. **文件编码问题**
   - 参考 `配置示例.md` 修改编码设置

5. **RAR文件问题**
   - 安装WinRAR或7-Zip
   - 或手动解压后使用文件夹模式

### 获取帮助
1. 运行 `测试工具.py` 进行环境诊断
2. 查看 `脚本使用说明.md` 获取详细说明
3. 查看 `配置示例.md` 了解自定义配置

## 📝 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 图形化界面实现
- ✅ 自动解压功能
- ✅ 完整的签名前后处理流程
- ✅ 详细的日志输出和错误处理

## 📄 许可证

本工具为内部使用工具，请勿外传。

## 🤝 支持

如有问题或建议，请联系开发团队。

---

**注意**: 使用前请仔细阅读 `脚本使用说明.md` 和 `配置示例.md` 文件。
