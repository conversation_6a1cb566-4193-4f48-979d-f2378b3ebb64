@echo off
chcp 65001 >nul
title 编译SFile工具

echo ================================================
echo 编译SFile工具
echo ================================================
echo.

echo 正在检查Visual Studio环境...

:: 尝试找到Visual Studio的vcvarsall.bat
set "VS_PATH="
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\VC\Auxiliary\Build\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio 12.0\VC\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio 12.0\VC\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\vcvarsall.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio 9.0\VC\vcvarsall.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio 9.0\VC\vcvarsall.bat"
)

if "%VS_PATH%"=="" (
    echo ❌ 错误: 未找到Visual Studio环境
    echo 请确保已安装Visual Studio并且路径正确
    echo.
    echo 支持的版本:
    echo - Visual Studio 2019 (Professional/Community)
    echo - Visual Studio 2017 (Professional/Community)
    echo - Visual Studio 2015 (14.0)
    echo - Visual Studio 2013 (12.0)
    echo - Visual Studio 2010 (10.0)
    echo - Visual Studio 2008 (9.0)
    pause
    exit /b 1
)

echo ✅ 找到Visual Studio: %VS_PATH%
echo.

echo 正在设置编译环境...
call "%VS_PATH%" x86

echo.
echo 正在编译SFile项目...
cd /d "%~dp0SFile"

if not exist "SFile.vcproj" (
    echo ❌ 错误: 未找到SFile.vcproj项目文件
    echo 请确保在正确的目录下运行此脚本
    pause
    exit /b 1
)

:: 尝试使用不同版本的编译器
if exist "SFile.sln" (
    echo 使用解决方案文件编译...
    devenv SFile.sln /build Release
) else (
    echo 使用项目文件编译...
    vcbuild SFile.vcproj Release
)

if errorlevel 1 (
    echo.
    echo ❌ 编译失败
    echo 请检查错误信息并修复代码问题
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功!

:: 检查生成的文件
if exist "Release\SFile.exe" (
    echo ✅ 找到编译后的文件: Release\SFile.exe
    
    :: 复制到上级目录
    copy "Release\SFile.exe" "..\SFile_new.exe" >nul
    if exist "..\SFile_new.exe" (
        echo ✅ 已复制到: SFile_new.exe
    )
) else if exist "Debug\SFile.exe" (
    echo ⚠️  找到调试版本: Debug\SFile.exe
    copy "Debug\SFile.exe" "..\SFile_new.exe" >nul
    if exist "..\SFile_new.exe" (
        echo ✅ 已复制到: SFile_new.exe
    )
) else (
    echo ❌ 未找到编译后的可执行文件
    echo 请检查编译输出目录
)

cd /d "%~dp0"

echo.
echo ================================================
echo 编译完成!
echo ================================================
echo.
echo 使用说明:
echo 1. 新的SFile.exe支持命令行参数
echo 2. 命令格式: SFile.exe -update "data文件夹路径" "sah文件路径"
echo 3. 示例: SFile.exe -update "C:\data" "C:\data.sah"
echo.
pause
