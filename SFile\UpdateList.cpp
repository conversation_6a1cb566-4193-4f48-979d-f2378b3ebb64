// UpdateList.cpp : implementation file
//

#include "stdafx.h"
#include "SFile.h"
#include "UpdateList.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define FILE_CLOSE(fh)   { if( fh != -1 ) _close(fh); fh = -1; } 
#define SAFE_DELETE(p)   { if(p) delete p; p = NULL; } 

/////////////////////////////////////////////////////////////////////////////
// UpdateList

CUpdateList::CUpdateList()
{
}

CUpdateList::~CUpdateList()
{
}


BEGIN_MESSAGE_MAP(CUpdateList, CListBox)
	//{{AFX_MSG_MAP(CUpdateList)
	ON_WM_DROPFILES()
	ON_WM_KEYDOWN()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CUpdateList message handlers

void CUpdateList::OnDropFiles(HDROP hDropInfo) 
{
	// TODO: Add your message handler code here and/or call default
	char szFileName[1000];	
	UINT count=0;
	UINT i;

	count=::DragQueryFile(hDropInfo, 0xFFFFFFFF, szFileName, _MAX_PATH);

	for(i=0; i < count; i++)
	{
		::DragQueryFile(hDropInfo, i, szFileName, _MAX_PATH);
		
		if( !AddDropFile( szFileName ) ) 
			break;
	}

	::DragFinish(hDropInfo);

	CListBox::OnDropFiles(hDropInfo);
}


bool CUpdateList::AddDropFile( char *szFileName )
{
	//CMainFrame* Frm = (CMainFrame*)::AfxGetMainWnd();	

	//���丮���� �˻�
	CFileFind finder;
	finder.FindFile(szFileName);
	finder.FindNextFile();

	//��Ʈ ���丮�� ���õǾ��ִ��� Ȯ�� 
	{
		CString str;
		GetParent()->GetDlgItemText( IDC_EDIT_ROOT, str );
		m_strRootDir = str;
		//��Ʈ���丮 ���� \\�� ������ ������ �߰����ش�.
		{
			char buf[2048];
			strcpy( buf, str );
			int i=strlen(buf)-1;
			if( i < 0 || buf[i] != '\\' )
			{
				strcat( buf, "\\" );
				m_strRootDir = buf;
				GetParent()->SetDlgItemText( IDC_EDIT_ROOT, buf );
			}
		}

		if( m_strRootDir.IsEmpty() )
		{
			str.Format( "��Ʈ ���丮�� ���õǾ� ���� �ʽ��ϴ�.(Check Root Directory!)" );
			AfxMessageBox( str, MB_OK );
			return false;
		}
	}

	if(finder.IsDirectory())
	{
		CString strRoot;
		GetParent()->GetDlgItemText( IDC_EDIT_ROOT, strRoot );
		m_strRootDir = strRoot;
		if( strnicmp( szFileName, m_strRootDir.GetBuffer(0), m_strRootDir.GetLength() ) != 0 )
		{
			CString buf;
			buf.Format( "��Ʈ���� ������ �ִ� ���丮�� �ƴմϴ�.(Check Root Directory!)\n\n%s", szFileName );
			AfxMessageBox( buf, MB_OK );
			return false;
		}
		
		SearchDir( finder.GetFilePath() );
	}
	else
	{
		CString strRoot;
		GetParent()->GetDlgItemText( IDC_EDIT_ROOT, strRoot );
		m_strRootDir = strRoot;
		if( strnicmp( szFileName, m_strRootDir.GetBuffer(0), m_strRootDir.GetLength() ) != 0 )
		{
			CString buf;
			buf.Format( "��Ʈ���� ������ �ִ� ������ �ƴմϴ�.(Check Root Directory!)\n\n%s", szFileName );
			AfxMessageBox( buf, MB_OK );
			return false;
		}
		else
		{
			if( !AddFile( m_updateL, szFileName, GetFileName(szFileName) ) )
			{
				CString buf;
				buf.Format( "add file err.\n\n%s", szFileName );
				AfxMessageBox( buf, MB_OK );
				return false;
			}

			AddString( szFileName + m_strRootDir.GetLength() );
		}
	}

	finder.Close();

	return true;
}

bool CUpdateList::AddFile( std::list<UpdateFile*> &list, char *path, char *name )
{
	UpdateFile *uf = new UpdateFile;
	SAFile *file = new SAFile;
	uf->file = file;

	CFile cfile;
	if( !cfile.Open( path, CFile::modeRead ) ) 
	{
		CString buf;
		buf.Format( "file open error.\n\n%s", path );
		return false;
	}

	strcpy( uf->szName, path );
	strcpy( uf->file->szName, name );
	uf->file->size = cfile.GetLength();
	uf->file->time = (long)GetTime( CString(path) );
	cfile.Close();

	m_updateL.push_back(uf);

	return true;
}

time_t CUpdateList::GetTime( CString &fileName )
{
	CFileStatus status; 
	CFile::GetStatus( fileName, status );
	
	return status.m_mtime.GetTime();
}


void CUpdateList::SearchDir(CString &dir)
{
	CString path;
	path = dir + "\\*.*";

	CFileFind finder;
	BOOL bWorking = finder.FindFile(path.GetBuffer(0));

	while (bWorking)
	{
		bWorking = finder.FindNextFile();
		
		//directory
		if(finder.IsDirectory())
		{
			if(!finder.IsDots())
			{
				CString folder = dir + '\\' + finder.GetFileName();
				SearchDir( folder );
			}
		}
		else
		{
			char tmp[2048];
			sprintf( tmp, "%s\\%s", dir.GetBuffer(0), (LPCTSTR) finder.GetFileName() );

			if( !AddFile( m_updateL, tmp, GetFileName(tmp) ) ) 
			{
				CString buf;
				buf.Format( "add file error\n\n%s", tmp );
				AfxMessageBox( buf, MB_OK );
				return;
			}

			AddString( tmp + m_strRootDir.GetLength() );
		}
	}

	finder.Close();
	
}

char* CUpdateList::GetFileName( char *path )
{
	char buf[2048];
	strcpy( buf, path );

	int i = strlen(path)-1;

	if( i < 0 ) return NULL;

	while(1)
	{
		if( i == 0 || buf[i] == '\\' || buf[i] == '/' )
		{
			if( i == 0 ) return path;
			
			if( strlen(path) > i+1 ) return &path[i+1];
			else return NULL; // ���ϳ��� \\�� ������� 
		}
		
		i--;
	}

	return NULL;
}

void CUpdateList::ReleaseList()
{
	std::list<char*>::iterator iter;

	while( m_updateL.size() )
	{
		UpdateFile *uf = *(m_updateL.begin());
		SAFE_DELETE( uf->file );
		SAFE_DELETE( uf );
		m_updateL.pop_front();
	}

	ResetContent();
}

void CUpdateList::DeleteList(int index)
{
	std::list<UpdateFile*>::iterator iter = m_updateL.begin();
	
	for( int i=0; i<index; i++) iter++;

	UpdateFile *uf = *iter;
	SAFE_DELETE( uf->file );
	SAFE_DELETE( uf );

	m_updateL.erase( iter );
	DeleteString( index );

}

void CUpdateList::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	// TODO: Add your message handler code here and/or call default
	if(nChar == VK_DELETE && GetSelCount() )
	{
		int i;

		int *buf = new int[ GetSelCount() ];
		GetSelItems( GetSelCount(), buf ); 	
		
		int cnt = GetSelCount();
		for( i=0; i< cnt; i++ )
		{
			DeleteList( buf[i] );
			//m_list.DeleteString( buf[i] );

			if( i+1 < cnt )
			{	
				buf[i+1] -= i+1; //������ ����ŭ �ε��� ���� 
			}
		}
		
		delete buf;
	}


	CListBox::OnKeyDown(nChar, nRepCnt, nFlags);
}

void CUpdateList::AddFolderRecursive( char *folderPath )
{
	// 递归添加文件夹中的所有文件
	CFileFind finder;
	CString searchPath;
	searchPath.Format("%s\\*.*", folderPath);

	BOOL bWorking = finder.FindFile(searchPath);

	while (bWorking)
	{
		bWorking = finder.FindNextFile();

		if (finder.IsDots())
			continue;

		CString filePath = finder.GetFilePath();

		if (finder.IsDirectory())
		{
			// 递归处理子文件夹
			AddFolderRecursive(filePath.GetBuffer(0));
		}
		else
		{
			// 添加文件到列表
			AddDropFile(filePath.GetBuffer(0));
		}
	}

	finder.Close();
}
