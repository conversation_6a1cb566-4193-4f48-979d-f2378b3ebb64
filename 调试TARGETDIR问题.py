#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试TARGETDIR路径问题
"""

def simulate_path_calculation():
    """模拟SFile中的路径计算问题"""
    
    # 模拟场景
    test_cases = [
        {
            "targetdir": "F:\\temp_data_en\\data\\",  # 注意末尾有反斜杠
            "full_path": "F:\\temp_data_en\\data\\test.txt",
            "expected": "test.txt"
        },
        {
            "targetdir": "F:\\temp_data_en\\data",   # 注意末尾没有反斜杠
            "full_path": "F:\\temp_data_en\\data\\test.txt", 
            "expected": "test.txt"
        },
        {
            "targetdir": "C:\\test\\data\\",
            "full_path": "C:\\test\\data\\subfolder\\file.dat",
            "expected": "subfolder\\file.dat"
        }
    ]
    
    print("=" * 60)
    print("调试TARGETDIR路径计算问题")
    print("=" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"TARGETDIR: '{case['targetdir']}'")
        print(f"完整路径: '{case['full_path']}'")
        print(f"期望结果: '{case['expected']}'")
        
        # 模拟原始的错误计算方式
        targetdir = case['targetdir']
        full_path = case['full_path']
        
        # 原始方式：szWorkFile = t.GetBuffer(0) + strlen(TARGETDIR) +1
        strlen_targetdir = len(targetdir)
        offset = strlen_targetdir + 1
        
        print(f"strlen(TARGETDIR): {strlen_targetdir}")
        print(f"偏移量: {offset}")
        
        if offset < len(full_path):
            result_original = full_path[offset:]
            print(f"原始计算结果: '{result_original}'")
        else:
            print("原始计算结果: [越界访问]")
        
        # 正确的计算方式
        if targetdir.endswith('\\'):
            clean_targetdir = targetdir[:-1]  # 移除末尾反斜杠
        else:
            clean_targetdir = targetdir
            
        if full_path.startswith(clean_targetdir + '\\'):
            result_correct = full_path[len(clean_targetdir) + 1:]
            print(f"正确计算结果: '{result_correct}'")
        else:
            print("正确计算结果: [路径不匹配]")
        
        # 检查是否匹配期望
        if 'result_correct' in locals() and result_correct == case['expected']:
            print("✅ 正确!")
        else:
            print("❌ 错误!")
    
    print("\n" + "=" * 60)
    print("问题分析:")
    print("1. 当TARGETDIR末尾有反斜杠时，strlen(TARGETDIR)+1会跳过太多字符")
    print("2. 这导致相对路径计算错误，产生乱码或问号")
    print("3. 需要在计算前规范化TARGETDIR路径")
    print("=" * 60)

def analyze_sfile_error():
    """分析SFile错误的具体原因"""
    print("\n" + "=" * 60)
    print("SFile错误分析")
    print("=" * 60)
    
    # 模拟您遇到的具体情况
    targetdir = "F:\\temp_data_en\\data\\"  # 我们设置的路径
    example_file = "F:\\temp_data_en\\data\\test.txt"
    
    print(f"设置的TARGETDIR: '{targetdir}'")
    print(f"处理的文件: '{example_file}'")
    
    # 模拟AddFile函数中的计算
    strlen_result = len(targetdir)
    offset = strlen_result + 1
    
    print(f"strlen(TARGETDIR): {strlen_result}")
    print(f"计算的偏移量: {offset}")
    print(f"文件路径长度: {len(example_file)}")
    
    if offset <= len(example_file):
        calculated_path = example_file[offset:]
        print(f"计算出的相对路径: '{calculated_path}'")
        
        # 检查是否包含无效字符
        if not calculated_path or calculated_path.startswith('\\'):
            print("❌ 计算结果无效，这就是问号的来源!")
        else:
            print("✅ 计算结果看起来正常")
    else:
        print("❌ 偏移量超出字符串长度，会导致越界访问!")
    
    print("\n解决方案:")
    print("1. 在设置TARGETDIR时不要添加末尾反斜杠")
    print("2. 或者在AddFile函数中正确处理末尾反斜杠")
    print("3. 使用CString操作而不是指针偏移")

if __name__ == "__main__":
    simulate_path_calculation()
    analyze_sfile_error()
    input("\n按回车键退出...")
