# SFile.exe 使用指南

## 🚨 常见问题解决

### 问题：路径中出现问号 (????)

这个问题通常是由于以下原因造成的：

1. **中文路径问题**：SFile.exe可能不支持中文路径
2. **路径长度问题**：路径过长可能导致显示异常
3. **编码问题**：系统编码与SFile.exe不匹配

## 🔧 解决方案

### 方案1：使用英文路径（推荐）

1. **创建英文工作目录**：
   ```
   C:\GameMaintenance\
   ├── data\           # 将data文件夹复制到这里
   ├── SFile.exe
   └── output\         # 输出目录
   ```

2. **避免中文路径**：
   - 不要使用包含中文的文件夹名
   - 不要将文件放在中文路径下
   - 使用简短的英文路径

### 方案2：手动操作SFile.exe

如果自动化失败，可以手动操作：

1. **启动SFile.exe**
2. **设置参数**：
   - SAH文件路径：选择或创建一个.sah文件
   - 根目录：设置为包含data文件夹的目录
   - 要更新的扩展名：保持默认 `*.*`
   - 存储文件名：选择 `update`

3. **添加文件**：
   - 将data文件夹拖拽到SFile界面的文件列表中
   - 或者使用"添加文件"功能手动添加

4. **开始处理**：
   - 点击"开始"按钮
   - 等待处理完成

## 🛠️ 运维工具集成

### 自动化处理流程

运维工具会尝试以下方法：

1. **命令行模式**（如果使用修改版SFile.exe）
2. **GUI模式调用**
3. **手动操作提示**

### 错误处理

如果自动化失败，工具会：

1. **显示详细错误信息**
2. **提供手动操作指南**
3. **保留中间文件供调试**

## 📁 文件结构要求

### 输入结构
```
data/
├── file1.dat
├── file2.dat
├── subfolder/
│   ├── file3.dat
│   └── file4.dat
└── ...
```

### 输出文件
- `update.saf` - 数据文件
- `update.sah` - 头文件

## 🔍 故障排除

### 1. 检查路径
```bash
# 确保路径不包含中文
echo %CD%
# 应该显示类似: C:\GameMaintenance
```

### 2. 检查文件权限
- 确保SFile.exe有执行权限
- 确保输出目录有写权限

### 3. 检查SFile.exe版本
- 使用原版SFile.exe
- 或使用修改后的SFile_new.exe

### 4. 手动测试
```bash
# 在命令行中测试
cd C:\GameMaintenance
SFile.exe
```

## 📝 最佳实践

### 1. 路径管理
- 使用短的英文路径
- 避免空格和特殊字符
- 使用绝对路径

### 2. 文件组织
```
工作目录/
├── tools/
│   ├── SFile.exe
│   └── Themida.exe
├── input/
│   └── data/
├── output/
│   ├── patch~/
│   ├── update.saf
│   └── update.sah
└── temp/
```

### 3. 批处理脚本示例
```batch
@echo off
cd /d "C:\GameMaintenance"
echo 开始处理data文件夹...
SFile.exe
echo 处理完成
pause
```

## 🔄 与运维工具的集成

### 自动化流程
1. 运维工具解压文件到临时目录
2. 查找data文件夹
3. 调用SFile.exe处理
4. 检查输出文件
5. 移动到patch~目录

### 手动介入点
如果自动化失败，在以下步骤手动介入：
1. 查看日志中的手动操作指南
2. 按照指南手动运行SFile.exe
3. 完成后重新运行运维工具

## 📞 技术支持

### 常见错误代码
- **路径问题**：显示问号或乱码
- **权限问题**：无法创建输出文件
- **版本问题**：SFile.exe崩溃或无响应

### 调试信息收集
1. 运维工具的完整日志
2. SFile.exe的错误截图
3. 文件路径和结构信息
4. 系统环境信息（Windows版本、编码设置）

---

**注意**：如果遇到持续问题，建议使用英文路径和手动操作模式，确保稳定性。
