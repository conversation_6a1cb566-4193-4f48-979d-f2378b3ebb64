#if !defined(AFX_SFILEDLG_H__B926911C_E925_418C_BBA6_41C960D3415C__INCLUDED_)
#define AFX_SFILEDLG_H__B926911C_E925_418C_BBA6_41C960D3415C__INCLUDED_

#include "SAFile.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SfileDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// SfileDlg dialog

class SfileDlg : public CDialog
{
// Construction
public:
	void SetControl( BOOL flgEnable );
	//DWORD WINAPI ThreadMakeDataFile( void far* para );
	void SaveHeader( SADir *dir );
	void SaveHeaderEmptyList( void );
	void AddFileList( CString path, CString name, unsigned long length, long time );
	void DeleteFileList( SADir *dir );
	void Release( void );
	bool ChkFileExt( const char *fileName );
	void AddFile( CString path, CString name );
	SADir *m_saDir;
	int m_fhW;
	int	m_fhWH;
	unsigned int m_nDirCnt;
	unsigned int m_nFileCnt;
	__int64 m_nFileSize;
	__int64 m_lOffset;
	unsigned int m_nVer;

	void SearchFile( CString strName );
	time_t GetTime( CString &fileName );


	SfileDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(SfileDlg)
	enum { IDD = IDD_DLG_SFILE };
	CSpinButtonCtrl	m_spinVer;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SfileDlg)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(SfileDlg)
	afx_msg void OnButtonFileAdd();
	virtual BOOL OnInitDialog();
	afx_msg void OnClose();
	afx_msg void OnButtonDir();
	afx_msg void OnRadio1();
	afx_msg void OnRadio2();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

// 全局函数声明
DWORD WINAPI ThreadMakeDataFile(LPVOID lpParam);

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEDLG_H__B926911C_E925_418C_BBA6_41C960D3415C__INCLUDED_)
