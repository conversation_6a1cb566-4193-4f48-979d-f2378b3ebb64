// MainFrm.cpp : implementation of the CMainFrame class
//

#include "stdafx.h"
#include "SFile.h"

#include "MainFrm.h"
#include "SfileDlg.h"
#include "SfileUpdateDlg.h"
#include "SfileExtractDlg.h"
#include "SfileDeleteListDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CMainFrame

IMPLEMENT_DYNCREATE(CMainFrame, CFrameWnd)

BEGIN_MESSAGE_MAP(CMainFrame, CFrameWnd)
	//{{AFX_MSG_MAP(CMainFrame)
	ON_WM_CREATE()
	ON_COMMAND(ID_SFILE_ADD, OnSfileAdd)
	ON_COMMAND(ID_SFILE_MERGE, OnSfileMerge)
	ON_COMMAND(ID_SFILE_EXTRACT, OnSfileExtract)
	ON_COMMAND(ID_SFILE_DELETELIST, OnSfileDeletelist)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

static UINT indicators[] =
{
	ID_SEPARATOR,           // status line indicator
	ID_INDICATOR_CAPS,
	ID_INDICATOR_NUM,
	ID_INDICATOR_SCRL,
};

/////////////////////////////////////////////////////////////////////////////
// CMainFrame construction/destruction

CMainFrame::CMainFrame()
{
	// TODO: add member initialization code here
	
}

CMainFrame::~CMainFrame()
{
	if( m_updateDlg )
	{
		m_updateDlg->DestroyWindow();
		delete m_updateDlg;
		m_updateDlg = NULL;
	}

	if( m_extractDlg )
	{
		m_extractDlg->DestroyWindow();
		delete m_extractDlg;
		m_extractDlg = NULL;

	}

	if( m_deleteListDlg	)
	{
		m_deleteListDlg->DestroyWindow();
		delete m_deleteListDlg;
		m_deleteListDlg = NULL;
	}
}

int CMainFrame::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CFrameWnd::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	if (!m_wndToolBar.CreateEx(this, TBSTYLE_FLAT, WS_CHILD | WS_VISIBLE | CBRS_TOP
		| CBRS_GRIPPER | CBRS_TOOLTIPS | CBRS_FLYBY | CBRS_SIZE_DYNAMIC) ||
		!m_wndToolBar.LoadToolBar(IDR_MAINFRAME))
	{
		TRACE0("Failed to create toolbar\n");
		return -1;      // fail to create
	}

	if (!m_wndStatusBar.Create(this) ||
		!m_wndStatusBar.SetIndicators(indicators,
		  sizeof(indicators)/sizeof(UINT)))
	{
		TRACE0("Failed to create status bar\n");
		return -1;      // fail to create
	}

	// TODO: Delete these three lines if you don't want the toolbar to
	//  be dockable
	m_wndToolBar.EnableDocking(CBRS_ALIGN_ANY);
	EnableDocking(CBRS_ALIGN_ANY);
	DockControlBar(&m_wndToolBar);

	m_updateDlg = NULL;
	m_extractDlg = NULL;
	m_deleteListDlg	= NULL;

	return 0;
}

BOOL CMainFrame::PreCreateWindow(CREATESTRUCT& cs)
{
	if( !CFrameWnd::PreCreateWindow(cs) )
		return FALSE;
	// TODO: Modify the Window class or styles here by modifying
	//  the CREATESTRUCT cs
	cs.cx = 350;
	cs.cy = 350;

	return TRUE;
}

/////////////////////////////////////////////////////////////////////////////
// CMainFrame diagnostics

#ifdef _DEBUG
void CMainFrame::AssertValid() const
{
	CFrameWnd::AssertValid();
}

void CMainFrame::Dump(CDumpContext& dc) const
{
	CFrameWnd::Dump(dc);
}

#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CMainFrame message handlers



void CMainFrame::OnSfileAdd() 
{
	//AfxMessageBox( "Can't aceess!" );
	//return;
	// TODO: Add your command handler code here
	SfileDlg fileDlg;
	
	fileDlg.DoModal();


}

void CMainFrame::OnSfileMerge() 
{
	// TODO: Add your command handler code here
	if( m_updateDlg )
	{
		m_updateDlg->DestroyWindow();
		delete m_updateDlg;
		m_updateDlg = NULL;
	}

	if( !m_updateDlg )
	{
		m_updateDlg = new SfileUpdateDlg;
		m_updateDlg->Create( IDD_DLG_SFILE_UPDATE, this );

		/*
		if( m_extractDlg ) 
		{
			CString str;
			m_extractDlg->GetDlgItemText( IDC_BUTTON_FILE_OPEN, str );
			m_updateDlg->SetDlgItemText( IDC_BUTTON_SAFILE, str.GetBuffer(0) );
		}
		*/
	}

	m_updateDlg->ShowWindow( SW_SHOW );
}

void CMainFrame::OnSfileExtract() 
{
	//AfxMessageBox( "Can't aceess!" );
	//return;
	// TODO: Add your command handler code here
	//SfileExtractDlg dlg;
	//m_extractDlg = &dlg;
	//dlg.DoModal();

	if( m_extractDlg )
	{
		m_extractDlg->DestroyWindow();
		delete m_extractDlg;
		m_extractDlg = NULL;
	}


	if( !m_extractDlg )
	{
		m_extractDlg = new SfileExtractDlg;
		m_extractDlg->Create( IDD_DLG_SFILE_EXTRACT, this );

		/*
		if( m_updateDlg ) 
		{
			CString str;
			m_updateDlg->GetDlgItemText( IDC_BUTTON_SAFILE, str );
			m_extractDlg->LoadSAFile( str.GetBuffer(0) ); 
			m_extractDlg->SetDlgItemEnable( true );
		}
		*/
	}

	m_extractDlg->ShowWindow( SW_SHOW );

}

void CMainFrame::OnSfileDeletelist() 
{
	//AfxMessageBox( "Can't aceess!" );
	//return;
	// TODO: Add your command handler code here
	//SfileDeleteListDlg dlg;
	//m_deleteListDlg = &dlg;
	//dlg.DoModal();

	if( m_deleteListDlg	)
	{
		m_deleteListDlg->DestroyWindow();
		delete m_deleteListDlg;
		m_deleteListDlg = NULL;
	}

	if( !m_deleteListDlg )
	{
		m_deleteListDlg = new SfileDeleteListDlg;
		m_deleteListDlg->Create( IDD_DLG_SFILE_DELETELIST, this );
	}

	m_deleteListDlg->ShowWindow( SW_SHOW );

}


void CMainFrame::OpenExtractView( char *fileName )
{
	if( !m_extractDlg )
	{
		m_extractDlg = new SfileExtractDlg;
		m_extractDlg->Create( IDD_DLG_SFILE_EXTRACT, this );
	}

	m_extractDlg->LoadSAFile( fileName ); 
	m_extractDlg->SetDlgItemEnable( true );

	m_extractDlg->ShowWindow( SW_SHOW );
}

void CMainFrame::OpenUpdateView( char *fileName )
{
	if( !m_updateDlg )
	{
		m_updateDlg = new SfileUpdateDlg;
		m_updateDlg->Create( IDD_DLG_SFILE_UPDATE, this );
	}

	m_updateDlg->SetDlgItemText( IDC_BUTTON_SAFILE, fileName );

	m_updateDlg->ShowWindow( SW_SHOW );
}

void CMainFrame::OpenUpdateViewAuto( char *dataFolder, char *sahFile )
{
	if( !m_updateDlg )
	{
		m_updateDlg = new SfileUpdateDlg;
		m_updateDlg->Create( IDD_DLG_SFILE_UPDATE, this );
	}

	// 设置SAH文件路径
	m_updateDlg->SetDlgItemText( IDC_BUTTON_SAFILE, sahFile );

	// 设置数据文件夹路径（父目录）
	char parentDir[MAX_PATH];
	strcpy( parentDir, dataFolder );

	// 找到最后一个反斜杠，获取父目录
	char *lastSlash = strrchr( parentDir, '\\' );
	if( lastSlash != NULL )
	{
		*lastSlash = '\0';  // 截断到父目录
		strcat( parentDir, "\\" );  // 确保以反斜杠结尾
	}

	m_updateDlg->SetDlgItemText( IDC_EDIT_ROOT, parentDir );

	// 不显示对话框，直接处理
	// m_updateDlg->ShowWindow( SW_HIDE );

	// 自动添加数据文件夹中的所有文件
	m_updateDlg->AutoAddDataFolder( dataFolder );

	// 自动开始处理
	m_updateDlg->AutoStart();
}
