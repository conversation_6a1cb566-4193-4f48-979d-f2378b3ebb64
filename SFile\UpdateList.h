#if !defined(AFX_UPDATELIST_H__6FF0C2EA_F796_417C_B2B4_F710C0754F4A__INCLUDED_)
#define AFX_UPDATELIST_H__6FF0C2EA_F796_417C_B2B4_F710C0754F4A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// UpdateList.h : header file
//

#include <list>
#include "safile.h"

struct UpdateFile
{
	char szName[2048];
	SAFile *file;
};

/////////////////////////////////////////////////////////////////////////////
// UpdateList window

class CUpdateList : public CListBox
{
public:
	CUpdateList();
	bool AddDropFile( char *szFileName );
	bool AddFile( std::list<UpdateFile*> &list, char *path, char *name );
	time_t GetTime( CString &fileName );
	void SearchDir(CString &dir);
	void ReleaseList();
	void DeleteList(int index);
	char* GetFileName( char *path );
	void AddFolderRecursive( char *folderPath );

public:
	CString m_strRootDir;
	std::list<UpdateFile*> m_updateL;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CUpdateList)
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CUpdateList();

	// Generated message map functions
protected:
	//{{AFX_MSG(CUpdateList)
	afx_msg void OnDropFiles(HDROP hDropInfo);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_UPDATELIST_H__6FF0C2EA_F796_417C_B2B4_F710C0754F4A__INCLUDED_)
