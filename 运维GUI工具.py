#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维流程图形化界面工具
提供图形化界面来处理运营包和技术包的自动化操作
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import zipfile
import rarfile
import shutil
import subprocess
import hashlib
import re
from pathlib import Path
from datetime import datetime

class MaintenanceGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("运维流程自动化工具")
        self.root.geometry("800x600")
        
        # 默认路径
        self.default_data_path = r"C:\维护数据"
        
        # 选中的文件夹
        self.selected_operation_folder = None
        self.selected_technical_folder = None
        
        # 工作目录
        self.work_dir = Path.cwd()
        self.patch_dir = self.work_dir / "patch~"
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 数据文件夹选择
        ttk.Label(main_frame, text="数据文件夹:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        data_frame = ttk.Frame(main_frame)
        data_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        data_frame.columnconfigure(0, weight=1)
        
        self.data_path_var = tk.StringVar(value=self.default_data_path)
        self.data_path_entry = ttk.Entry(data_frame, textvariable=self.data_path_var)
        self.data_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(data_frame, text="浏览", command=self.browse_data_folder).grid(row=0, column=1)
        
        # 文件夹选择区域
        folder_frame = ttk.LabelFrame(main_frame, text="文件夹选择 (按住Ctrl可多选)", padding="10")
        folder_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        folder_frame.columnconfigure(0, weight=1)
        folder_frame.columnconfigure(1, weight=1)
        
        # 运营文件夹
        ttk.Label(folder_frame, text="运营文件夹:").grid(row=0, column=0, sticky=tk.W)
        self.operation_listbox = tk.Listbox(folder_frame, height=6, selectmode=tk.EXTENDED)
        self.operation_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 技术文件夹
        ttk.Label(folder_frame, text="技术文件夹:").grid(row=0, column=1, sticky=tk.W)
        self.technical_listbox = tk.Listbox(folder_frame, height=6, selectmode=tk.EXTENDED)
        self.technical_listbox.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        
        # 刷新按钮
        ttk.Button(folder_frame, text="刷新文件夹列表", command=self.refresh_folder_list).grid(row=2, column=0, columnspan=2, pady=10)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="签名前处理", command=self.start_pre_signature_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="签名后处理", command=self.start_post_signature_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, text="状态:").grid(row=3, column=0, sticky=tk.W)
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=3, column=1, sticky=tk.W)
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="日志输出", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 初始化
        self.refresh_folder_list()
        self.log("运维工具已启动")
        
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def error_log(self, message):
        """添加错误日志"""
        self.log(f"❌ ERROR: {message}")
        
    def success_log(self, message):
        """添加成功日志"""
        self.log(f"✅ SUCCESS: {message}")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def browse_data_folder(self):
        """浏览数据文件夹"""
        folder = filedialog.askdirectory(initialdir=self.data_path_var.get())
        if folder:
            self.data_path_var.set(folder)
            self.refresh_folder_list()
            
    def refresh_folder_list(self):
        """刷新文件夹列表"""
        data_path = Path(self.data_path_var.get())
        
        # 清空列表
        self.operation_listbox.delete(0, tk.END)
        self.technical_listbox.delete(0, tk.END)
        
        if not data_path.exists():
            self.error_log(f"数据文件夹不存在: {data_path}")
            return
            
        try:
            # 获取所有文件夹和压缩文件
            items = []
            for item in data_path.iterdir():
                if item.is_dir():
                    items.append(item.name)
                elif item.suffix.lower() in ['.zip', '.rar']:
                    items.append(item.name)
                    
            # 分类添加到列表框
            for item in sorted(items):
                # 简单的分类逻辑，可以根据实际情况调整
                if any(keyword in item.lower() for keyword in ['运营', 'operation', 'ops']):
                    self.operation_listbox.insert(tk.END, item)
                elif any(keyword in item.lower() for keyword in ['技术', 'technical', 'tech']):
                    self.technical_listbox.insert(tk.END, item)
                else:
                    # 默认都添加到两个列表中
                    self.operation_listbox.insert(tk.END, item)
                    self.technical_listbox.insert(tk.END, item)
                    
            self.log(f"已刷新文件夹列表，找到 {len(items)} 个项目")
            
        except Exception as e:
            self.error_log(f"刷新文件夹列表失败: {e}")
            
    def get_selected_folders(self):
        """获取选中的文件夹"""
        data_path = Path(self.data_path_var.get())
        
        # 获取运营文件夹选择
        operation_selections = self.operation_listbox.curselection()
        operation_folders = [data_path / self.operation_listbox.get(i) for i in operation_selections]
        
        # 获取技术文件夹选择
        technical_selections = self.technical_listbox.curselection()
        technical_folders = [data_path / self.technical_listbox.get(i) for i in technical_selections]
        
        return operation_folders, technical_folders
        
    def extract_archive(self, archive_path, extract_to):
        """解压压缩文件"""
        archive_path = Path(archive_path)
        extract_to = Path(extract_to)
        
        self.log(f"开始解压: {archive_path.name}")
        
        try:
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
            elif archive_path.suffix.lower() == '.rar':
                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_to)
            else:
                raise ValueError(f"不支持的压缩格式: {archive_path.suffix}")
                
            self.success_log(f"解压完成: {archive_path.name}")
            return True
            
        except Exception as e:
            self.error_log(f"解压失败 {archive_path.name}: {e}")
            return False
            
    def find_client_folder(self, base_path):
        """在指定路径下查找Client或client文件夹"""
        base_path = Path(base_path)
        
        # 递归查找Client或client文件夹
        for root, dirs, files in os.walk(base_path):
            for dir_name in dirs:
                if dir_name.lower() == 'client':
                    return Path(root) / dir_name
                    
        return None
        
    def start_pre_signature_processing(self):
        """开始签名前处理"""
        def process():
            try:
                self.progress_var.set("处理中...")
                self.log("=" * 50)
                self.log("开始签名前处理")
                
                operation_folders, technical_folders = self.get_selected_folders()
                
                if not operation_folders and not technical_folders:
                    self.error_log("请至少选择一个文件夹")
                    return
                    
                # 处理运营文件夹
                if operation_folders:
                    self.process_operation_folders(operation_folders)
                    
                # 处理技术文件夹
                if technical_folders:
                    self.process_technical_folders(technical_folders)
                    
                self.success_log("签名前处理完成!")
                self.log("下一步: 将game.exe发送给SP小组进行签名")
                
            except Exception as e:
                self.error_log(f"签名前处理失败: {e}")
            finally:
                self.progress_var.set("就绪")
                
        # 在新线程中运行，避免界面冻结
        threading.Thread(target=process, daemon=True).start()
        
    def process_operation_folders(self, operation_folders):
        """处理运营文件夹"""
        self.log("开始处理运营文件夹...")

        for folder_path in operation_folders:
            self.log(f"处理运营文件夹: {folder_path.name}")

            # 如果是压缩文件，先解压
            if folder_path.suffix.lower() in ['.zip', '.rar']:
                extract_dir = folder_path.parent / f"{folder_path.stem}_extracted"
                if extract_dir.exists():
                    shutil.rmtree(extract_dir)
                extract_dir.mkdir()

                if not self.extract_archive(folder_path, extract_dir):
                    continue

                # 在解压目录中查找Client文件夹
                client_folder = self.find_client_folder(extract_dir)
            else:
                # 直接在文件夹中查找Client文件夹
                client_folder = self.find_client_folder(folder_path)

            if not client_folder:
                self.error_log(f"未找到Client文件夹: {folder_path.name}")
                continue

            # 查找data文件夹
            data_folder = client_folder / "data"
            if not data_folder.exists():
                self.error_log(f"未找到data文件夹: {client_folder}")
                continue

            self.log(f"找到data文件夹: {data_folder}")

            # 使用SFile.exe打包
            if not self.pack_data_folder(data_folder):
                continue

        self.success_log("运营文件夹处理完成")

    def process_technical_folders(self, technical_folders):
        """处理技术文件夹"""
        self.log("开始处理技术文件夹...")

        for folder_path in technical_folders:
            self.log(f"处理技术文件夹: {folder_path.name}")

            # 如果是压缩文件，先解压
            if folder_path.suffix.lower() in ['.zip', '.rar']:
                extract_dir = folder_path.parent / f"{folder_path.stem}_extracted"
                if extract_dir.exists():
                    shutil.rmtree(extract_dir)
                extract_dir.mkdir()

                if not self.extract_archive(folder_path, extract_dir):
                    continue

                # 在解压目录中查找Client文件夹
                client_folder = self.find_client_folder(extract_dir)
            else:
                # 直接在文件夹中查找Client文件夹
                client_folder = self.find_client_folder(folder_path)

            if not client_folder:
                self.error_log(f"未找到Client文件夹: {folder_path.name}")
                continue

            # 查找game.exe
            game_exe = client_folder / "game.exe"
            if not game_exe.exists():
                self.error_log(f"未找到game.exe: {client_folder}")
                continue

            self.log(f"找到game.exe: {game_exe}")

            # 使用Themida.exe加壳
            if not self.protect_game_exe(game_exe):
                continue

        self.success_log("技术文件夹处理完成")

    def pack_data_folder(self, data_folder):
        """使用SFile.exe打包data文件夹"""
        sfile_exe = self.work_dir / "SFile.exe"
        if not sfile_exe.exists():
            self.error_log(f"SFile.exe不存在: {sfile_exe}")
            return False

        self.log("使用SFile.exe打包data文件夹...")
        try:
            result = subprocess.run([str(sfile_exe), str(data_folder)],
                                  capture_output=True, text=True, cwd=self.work_dir)

            if result.returncode != 0:
                self.error_log(f"SFile.exe执行失败: {result.stderr}")
                return False

            # 检查生成的文件
            update_saf = self.work_dir / "update.saf"
            update_sah = self.work_dir / "update.sah"

            if not update_saf.exists() or not update_sah.exists():
                self.error_log("未找到生成的update.saf或update.sah文件")
                return False

            # 创建patch~文件夹
            if self.patch_dir.exists():
                shutil.rmtree(self.patch_dir)
            self.patch_dir.mkdir()

            # 移动文件到patch~文件夹
            shutil.move(str(update_saf), str(self.patch_dir / "update.saf"))
            shutil.move(str(update_sah), str(self.patch_dir / "update.sah"))

            self.success_log("data文件夹打包完成")
            return True

        except Exception as e:
            self.error_log(f"打包data文件夹失败: {e}")
            return False

    def protect_game_exe(self, game_exe):
        """使用Themida.exe对game.exe加壳"""
        themida_exe = self.work_dir / "Themida.exe"
        if not themida_exe.exists():
            self.error_log(f"Themida.exe不存在: {themida_exe}")
            return False

        self.log("使用Themida.exe对game.exe进行加壳...")
        try:
            game_protected = game_exe.parent / "game_protected.exe"

            result = subprocess.run([str(themida_exe), str(game_exe), str(game_protected)],
                                  capture_output=True, text=True, cwd=self.work_dir)

            if result.returncode != 0:
                self.error_log(f"Themida.exe执行失败: {result.stderr}")
                return False

            if not game_protected.exists():
                self.error_log("未找到生成的game_protected.exe文件")
                return False

            # 删除原game.exe并重命名
            game_exe.unlink()
            game_protected.rename(game_exe)

            self.success_log(f"game.exe加壳完成: {game_exe}")
            return True

        except Exception as e:
            self.error_log(f"game.exe加壳失败: {e}")
            return False

    def start_post_signature_processing(self):
        """开始签名后处理"""
        # 选择签名后的文件
        signed_file = filedialog.askopenfilename(
            title="选择签名后的game.exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )

        if not signed_file:
            return

        def process():
            try:
                self.progress_var.set("处理中...")
                self.log("=" * 50)
                self.log("开始签名后处理")

                # 调用签名后处理逻辑
                self.process_signed_game_exe(signed_file)

                self.success_log("签名后处理完成!")

            except Exception as e:
                self.error_log(f"签名后处理失败: {e}")
            finally:
                self.progress_var.set("就绪")

        threading.Thread(target=process, daemon=True).start()

    def process_signed_game_exe(self, signed_game_path):
        """处理签名后的game.exe"""
        signed_game_path = Path(signed_game_path)

        # 检查patch~文件夹是否存在
        if not self.patch_dir.exists():
            self.error_log(f"patch~文件夹不存在，请先运行签名前处理")
            return False

        # 复制签名后的文件到patch~文件夹
        target_game_path = self.patch_dir / "game.exe"
        shutil.copy2(signed_game_path, target_game_path)
        self.log(f"已将签名后的文件复制到: {target_game_path}")

        # 创建zip压缩包
        zip_path = self.create_patch_zip()
        if not zip_path:
            return False

        # 计算MD5值
        md5_value = self.calculate_md5(zip_path)
        self.log(f"MD5值: {md5_value}")

        # 更新launcher.shaiya配置
        new_version = self.update_launcher_config(md5_value)
        if not new_version:
            return False

        # 重命名zip为bin文件
        bin_path = self.rename_zip_to_bin(zip_path, md5_value)

        self.success_log(f"签名后处理完成!")
        self.log(f"新版本号: {new_version}")
        self.log(f"最终文件: {bin_path}")

        return True

    def create_patch_zip(self):
        """创建patch~.zip压缩包"""
        self.log("创建patch~.zip压缩包...")

        zip_path = self.work_dir / "patch~.zip"

        if zip_path.exists():
            zip_path.unlink()

        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in self.patch_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.patch_dir)
                        zipf.write(file_path, arcname)
                        self.log(f"添加文件到zip: {arcname}")

            self.success_log(f"patch~.zip创建完成: {zip_path}")
            return zip_path

        except Exception as e:
            self.error_log(f"创建zip文件失败: {e}")
            return None

    def calculate_md5(self, file_path):
        """计算文件的MD5值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def update_launcher_config(self, md5_value):
        """更新launcher.shaiya配置文件"""
        launcher_config = self.work_dir / "launcher.shaiya"

        if not launcher_config.exists():
            self.error_log(f"launcher.shaiya文件不存在: {launcher_config}")
            return None

        try:
            with open(launcher_config, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找当前版本号
            version_pattern = r'Version=0x([0-9a-fA-F]+)'
            version_match = re.search(version_pattern, content)

            if not version_match:
                self.error_log("未找到Version配置项")
                return None

            current_version_hex = version_match.group(1)
            current_version_int = int(current_version_hex, 16)
            new_version_int = current_version_int + 1
            new_version_hex = f"{new_version_int:08x}"
            new_version_str = f"0x{new_version_hex}"

            self.log(f"当前版本: 0x{current_version_hex}")
            self.log(f"新版本: {new_version_str}")

            # 替换版本号
            new_content = re.sub(version_pattern, f'Version={new_version_str}', content)

            # 在[Patch]段落末尾添加新的MD5行
            patch_section_pattern = r'(\[Patch\].*?)(\n\[|\Z)'
            patch_match = re.search(patch_section_pattern, new_content, re.DOTALL)

            if patch_match:
                patch_section = patch_match.group(1)
                new_md5_line = f"\n{new_version_str}={md5_value}"
                new_patch_section = patch_section + new_md5_line
                new_content = new_content.replace(patch_section, new_patch_section)
            else:
                self.error_log("未找到[Patch]段落")
                return None

            # 写回文件
            with open(launcher_config, 'w', encoding='utf-8') as f:
                f.write(new_content)

            self.success_log("launcher.shaiya配置文件更新完成")
            self.log(f"添加行: {new_version_str}={md5_value}")

            return new_version_str

        except Exception as e:
            self.error_log(f"更新配置文件失败: {e}")
            return None

    def rename_zip_to_bin(self, zip_path, md5_value):
        """将patch~.zip重命名为MD5值.bin"""
        bin_path = self.work_dir / f"{md5_value}.bin"

        if bin_path.exists():
            bin_path.unlink()

        zip_path.rename(bin_path)
        self.log(f"文件已重命名为: {bin_path}")

        return bin_path

if __name__ == "__main__":
    root = tk.Tk()
    app = MaintenanceGUI(root)
    root.mainloop()
