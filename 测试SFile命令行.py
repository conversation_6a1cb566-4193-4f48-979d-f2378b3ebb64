#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SFile.exe命令行功能
"""

import subprocess
import os
import sys
from pathlib import Path
import time

def test_sfile_command_line():
    """测试SFile.exe命令行功能"""
    print("=" * 60)
    print("测试SFile.exe命令行功能")
    print("=" * 60)
    
    # 检查SFile.exe是否存在
    current_dir = Path.cwd()
    sfile_exe = current_dir / "SFile.exe"
    sfile_new = current_dir / "SFile_new.exe"
    
    if sfile_new.exists():
        sfile_path = sfile_new
        print(f"✅ 找到修改后的SFile: {sfile_path}")
    elif sfile_exe.exists():
        sfile_path = sfile_exe
        print(f"⚠️  找到原版SFile: {sfile_path}")
        print("   建议先编译修改后的版本")
    else:
        print("❌ 未找到SFile.exe")
        return False
    
    # 创建测试数据文件夹
    test_data_dir = current_dir / "test_data"
    if test_data_dir.exists():
        import shutil
        shutil.rmtree(test_data_dir)
    
    test_data_dir.mkdir()
    
    # 创建一些测试文件
    test_files = [
        "test1.txt",
        "test2.dat",
        "config.ini"
    ]
    
    for filename in test_files:
        test_file = test_data_dir / filename
        with open(test_file, 'w') as f:
            f.write(f"Test content for {filename}\n")
            f.write("This is a test file for SFile.exe\n")
            f.write(f"Created at: {time.ctime()}\n")
    
    print(f"✅ 创建测试数据文件夹: {test_data_dir}")
    print(f"   包含 {len(test_files)} 个测试文件")
    
    # 测试命令行调用
    print("\n🔍 测试SFile.exe命令行调用...")
    
    try:
        # 调用SFile.exe -make
        cmd = [str(sfile_path), "-make", str(test_data_dir)]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=current_dir,
            timeout=60
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"错误输出:\n{result.stderr}")
        
        # 等待文件生成
        print("\n⏳ 等待文件生成...")
        time.sleep(2)
        
        # 检查生成的文件
        update_saf = current_dir / "update.saf"
        update_sah = current_dir / "update.sah"
        
        print("\n📁 检查生成的文件:")
        
        files_generated = []
        
        if update_saf.exists():
            size = update_saf.stat().st_size
            files_generated.append(f"update.saf ({size} bytes)")
            print(f"✅ update.saf - {size} bytes")
        else:
            print("❌ update.saf - 未生成")
        
        if update_sah.exists():
            size = update_sah.stat().st_size
            files_generated.append(f"update.sah ({size} bytes)")
            print(f"✅ update.sah - {size} bytes")
        else:
            print("❌ update.sah - 未生成")
        
        # 检查其他可能的输出文件
        print("\n🔍 检查其他可能的输出文件:")
        for file in current_dir.glob("*.saf"):
            if file.name != "update.saf":
                size = file.stat().st_size
                print(f"📦 {file.name} - {size} bytes")
                
        for file in current_dir.glob("*.sah"):
            if file.name != "update.sah":
                size = file.stat().st_size
                print(f"📦 {file.name} - {size} bytes")
        
        # 总结结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        if len(files_generated) >= 2:
            print("🎉 测试成功!")
            print(f"生成的文件: {', '.join(files_generated)}")
            print("SFile.exe命令行功能正常工作")
        elif len(files_generated) == 1:
            print("⚠️  部分成功")
            print(f"生成的文件: {', '.join(files_generated)}")
            print("可能需要检查SFile.exe的实现")
        else:
            print("❌ 测试失败")
            print("未生成任何输出文件")
            print("可能的原因:")
            print("1. SFile.exe不支持命令行参数")
            print("2. 路径或参数格式错误")
            print("3. 需要重新编译SFile.exe")
        
        return len(files_generated) >= 2
        
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            if test_data_dir.exists():
                import shutil
                shutil.rmtree(test_data_dir)
                print(f"\n🧹 已清理测试数据: {test_data_dir}")
        except:
            pass

def main():
    """主函数"""
    print("SFile.exe 命令行功能测试工具")
    
    success = test_sfile_command_line()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试通过!")
        print("可以在运维工具中使用新的命令行功能")
    else:
        print("❌ 测试失败")
        print("建议:")
        print("1. 运行 '编译SFile.bat' 重新编译")
        print("2. 检查SFile源码修改是否正确")
        print("3. 使用原版SFile.exe的GUI模式")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
