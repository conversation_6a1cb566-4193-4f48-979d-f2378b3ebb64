// SfileUpdateDlg.cpp : implementation file
//

#include "stdafx.h"
#include "SFile.h"
#include "SAFile.h"
#include "SfileUpdateDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


#define FILE_CLOSE(fh)   { if( fh != -1 ) _close(fh); fh = -1; } 
#define SAFE_DELETE(p)   { if(p) delete p; p = NULL; } 

#define MAX_FILE_PATH	2048
#define BUFSIZE	 500000

static char *g_buf = NULL;

/////////////////////////////////////////////////////////////////////////////
// SfileUpdateDlg dialog


SfileUpdateDlg::SfileUpdateDlg(CWnd* pParent /*=NULL*/)
	: CDialog(SfileUpdateDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(SfileUpdateDlg)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void SfileUpdateDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(SfileUpdateDlg)
	DDX_Control(pDX, IDC_PROGRESS_UPDATE, m_prgress);
	DDX_Control(pDX, IDC_LIST_FILES, m_list);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(SfileUpdateDlg, CDialog)
	//{{AFX_MSG_MAP(SfileUpdateDlg)
	ON_BN_CLICKED(IDC_BUTTON_SAFILE, OnButtonSafile)
	ON_BN_CLICKED(IDC_BUTTON_ROOT, OnButtonRoot)
	ON_WM_DESTROY()
	ON_BN_CLICKED(IDC_BUTTON_START, OnButtonStart)
	ON_WM_KEYDOWN()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()


DWORD WINAPI ThreadUpdateDataFile( void far* para )
{
	SfileUpdateDlg *dlg = (SfileUpdateDlg*)para;

	unsigned int tmp[10]; 
	char buf[2048];

	_read( dlg->m_fhSH, &buf[0], 3 ); //SAH
	_read( dlg->m_fhSH, &dlg->m_nSrcVer, sizeof(unsigned int) );
	_read( dlg->m_fhSH, &dlg->m_nSrcFileCnt, sizeof(unsigned int) );
	_read( dlg->m_fhSH, tmp, sizeof(unsigned int)*10 );
	
	dlg->m_nUpdateFileCnt = dlg->m_list.m_updateL.size();

	dlg->ReadSAFile( dlg->m_fhSH, dlg->m_saDirS );
	dlg->ReadEmptyList( dlg->m_fhSH );
	dlg->ReadGarbageSize( dlg->m_fhSH );

	//������Ʈ ���ϸ���Ʈ �����
	char path[MAX_FILE_PATH];
	memset( path, NULL, sizeof(path) );
	dlg->MakeUpdateFileList();	

	dlg->m_prgress.SetRange32(0, dlg->m_nUpdateFileCnt );
	dlg->m_prgress.SetPos(0);
	dlg->m_nProgressCnt = 0;

	//������Ʈ!
	if( !dlg->CopyUpdateFile() )
	{
		AfxMessageBox( "Update error : CopyUpdateFile", MB_OK );
		dlg->Release();
		return 0;
	}


	//����Ʈ ���� 
	//empty����Ʈ ����: �������� �������� vs ������ 
	//700���� �Ѿ�� 500���� �����.
	//(������ �뷮 ���� )
	bool bLimitEmpty = false;
	
	if( dlg->m_emptyL.size() > 700 )
	{
		//�տ��� ���� �ڸ���.
		int nNumCut = dlg->m_emptyL.size() - 500;

		std::list<SAFile*>::iterator iter;
		while(nNumCut--)
		{
			iter = dlg->m_emptyL.begin();
			dlg->m_dwGarbageSize += (*iter)->size;

			delete *iter;
			dlg->m_emptyL.pop_front();
		}

		bLimitEmpty = true;
	}


	FILE_CLOSE( dlg->m_fhSH );
	dlg->m_fhSH = _open( dlg->m_strSrcH.GetBuffer(0), _O_WRONLY | _O_TRUNC | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );
	//dlg->m_fhSH = _open( dlg->m_strSrcH.GetBuffer(0), _O_WRONLY | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );
	if( dlg->m_fhSH == -1 ) 
	{	
		AfxMessageBox( "Update error : Last", MB_OK );
		dlg->Release();
		return 0;
	}

	//----------------------------------------------------------
	//�������( ������ ���ο� Ÿ������!)
	char *id = "SAH";
	_write( dlg->m_fhSH, id, sizeof(char)*3 );
	_write( dlg->m_fhSH, &dlg->m_nSrcVer, sizeof(unsigned int) );
	unsigned int fileCnt = dlg->m_nSrcFileCnt + dlg->m_nUpdateFileCnt;
	_write( dlg->m_fhSH, &fileCnt, sizeof(unsigned int) );
	_write( dlg->m_fhSH, dlg->m_reserve, sizeof(unsigned int)*10 );

	dlg->SaveHeader( dlg->m_saDirS );
	dlg->SaveHeaderEmptyList( dlg->m_fhSH );
	dlg->SaveHeaderGarbageSize( dlg->m_fhSH );
	//----------------------------------------------------------

	//
	dlg->Release();


	AfxMessageBox( "�Ϸ�! (Complete)", MB_OK );

	//����Ʈ ����� �ʱ�ȭ!
	dlg->m_list.ResetContent();
	dlg->m_prgress.SetPos(0);
	
	dlg->SetDlgItemEnable( TRUE );

	dlg->SendMessage( WM_CLOSE );

	return 0;
}

/////////////////////////////////////////////////////////////////////////////
// SfileUpdateDlg message handlers

void SfileUpdateDlg::OnButtonSafile() 
{
	// TODO: Add your control notification handler code here
	OPENFILENAME OFN;
	char lpstrFile[MAX_FILE_PATH]="";

	memset(&OFN,0,sizeof(OFN));

	OFN.lStructSize = sizeof(OFN);
	OFN.hwndOwner = this->m_hWnd;
	OFN.lpstrFilter = "sah Files (*.sah)\0*.sah\0All Files (*.*)\0*.*\0";
	OFN.lpstrDefExt = "*.sah";
	OFN.lpstrFile = lpstrFile;
	OFN.nMaxFile = MAX_FILE_PATH;
	OFN.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_ENABLESIZING;

	if(GetOpenFileName(&OFN) == 0)
		return;

	//CString str;
	//str = str + "sah���Ͽ���: " + OFN.lpstrFile;
	//SetDlgItemText( IDC_BUTTON_FILE_OPEN, str.GetBuffer(0) );

	//LoadSAFile( OFN.lpstrFile ); 

	//SetDlgItemEnable( true );

	SetDlgItemText( IDC_BUTTON_SAFILE, OFN.lpstrFile );
	
}

void SfileUpdateDlg::OnButtonRoot() 
{
	// TODO: Add your control notification handler code here
	BROWSEINFO info;
	ITEMIDLIST *item;
	char buf[MAX_PATH];
	memset( &info, 0, sizeof(info) );

	info.hwndOwner = this->m_hWnd;
	info.pszDisplayName = buf; 
	info.lpszTitle = "Select Root Folder";
	info.ulFlags = BIF_RETURNONLYFSDIRS;

	if( (item = SHBrowseForFolder(&info) ) != NULL )
	{
		SHGetPathFromIDList(item, buf); 
		SetDlgItemText( IDC_EDIT_ROOT, buf );
	}

}
/*
bool SfileUpdateDlg::LoadSAFile( char *filename )
{
	int fh;

	fh = _open( filename, _O_RDONLY | _O_BINARY );
	if( fh == -1 ) return false;


	//���ο� �������� Ȯ�� 
	char buf[3];
	unsigned int nVer;
	unsigned int nFileCnt;
	unsigned int tmp[10]; //40 byte ���� 

	_read( fh, &buf[0], 3 ); //SAH
	_read( fh, &nVer, sizeof(unsigned int) );
	_read( fh, &nFileCnt, sizeof(unsigned int) );
	_read( fh, tmp, sizeof(unsigned int)*10 );

	//����б� 
	m_pSADir = new SADir;
	ReadSAFile( fh, m_pSADir );

	//����ִ� ����Ʈ
	ReadEmptyList( fh ); 
	
	//������ �뷮�� �д´�.
	unsigned int xx_size;
	bool bXXSize = false;
	if( _read( fh, &xx_size, sizeof(unsigned int) ) )
	{
		bXXSize = true;
	}
	
	
	_close(fh);

	//root�����̸� ���Ƿ� �����ߴ�
	strcpy( m_pSADir->szName, "data" );

	CString str;
	m_strOpenFileName = filename;
	str.Format( "%s", filename );
	SetDlgItemText( IDC_BUTTON_SAFILE, str.GetBuffer(0) );
	
	return true;
}
*/

void SfileUpdateDlg::ReadEmptyList( int &fh )
{
	unsigned int cnt;
	int strSize;
	SAFile *file;
	std::list<SAFile*>::iterator iter;

	_read( fh, &cnt, sizeof(unsigned int) );
	
	for( int i=0; i < cnt; i++ )
	{
		file = new SAFile;
		//szName
		_read( fh, &strSize, sizeof(int) );
		_read( fh, file->szName, strSize );
		_read( fh, &file->offset, sizeof(__int64) );

		//size
		_read( fh, &file->size, sizeof(unsigned long) );
		//time
		_read( fh, &file->time, sizeof(long) );

		//�뷮 ���������� ����!! 
		for( iter = m_emptyL.begin(); iter != m_emptyL.end(); iter++ )
		{
			if( file->size <= (*iter)->size ) break;
		}	
		
		if( iter != m_emptyL.end() )	
			m_emptyL.insert( iter, file );
		else							
			m_emptyL.push_back( file );

	}



}


void SfileUpdateDlg::DeleteFileList(SADir *dir)
{
	if( dir	)
	{
		//child
		SADIter i = dir->dirL.begin();
		for( ; i != dir->dirL.end(); i++ )
		{
			DeleteFileList( (SADir*)*i);
		}

		SAFIter j = dir->fileL.begin();
		for( ; j != dir->fileL.end(); j++ )
		{
			delete *j;
		}

		delete dir;
	}
}


void SfileUpdateDlg::OnDestroy() 
{
	CDialog::OnDestroy();
	
	// TODO: Add your message handler code here
	Release();
}

BOOL SfileUpdateDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	char buf[MAX_PATH];
	char path[MAX_PATH];
    GetCurrentDirectory(MAX_PATH, path);

	sprintf( buf, "%s\\DATA\\", path );
	SetDlgItemText( IDC_EDIT_ROOT, buf );

	sprintf( buf, "%s\\data.sah", path );
	SetDlgItemText( IDC_BUTTON_SAFILE, buf );

	m_saDirS = NULL;

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void SfileUpdateDlg::OnButtonStart() 
{
	// TODO: Add your control notification handler code here
	//���Ͼ����� �� 
	if( m_list.GetCount() == 0 ) return;

	SetDlgItemEnable( FALSE );

	m_fhS = -1;
	m_fhSH = -1;
	m_fhTmp = -1;
	m_saDirS = new SADir;
	g_buf = new char[BUFSIZE];

	//������ �����ϴ��� Ȯ�� 
	GetDlgItemText( IDC_BUTTON_SAFILE, m_strSrcH );
	
	char buf[2048];
	strcpy( buf, m_strSrcH.GetBuffer(0) );
	buf[strlen(buf)-1] = 'f';
	m_strSrcF = buf;

	if( _access( m_strSrcH.GetBuffer(0), 0 ) != 0 || _access( m_strSrcF, 0 ) != 0 )
	{
		AfxMessageBox( "SAFile������ ã���� �����ϴ�.(Check SAFile header/file)", MB_OK );
		return;
	}

	//src����б�
	m_fhSH = _open( m_strSrcH.GetBuffer(0), _O_RDONLY | _O_BINARY );
	if( m_fhSH == -1 )
	{
		CString str;
		str.Format( "%s������ �� �� �����ϴ�.(sah file open err)", m_strSrcH.GetBuffer(0) );
		AfxMessageBox( str, MB_OK );
		return;
	}

	DWORD threadID = 0;
	::CreateThread( 0, 0, ThreadUpdateDataFile, this, 0, &threadID );

}

void SfileUpdateDlg::Release()
{
	SAFE_DELETE( g_buf );
	FILE_CLOSE( m_fhS );
	FILE_CLOSE( m_fhU );
	FILE_CLOSE( m_fhSH );
	FILE_CLOSE( m_fhUH );

	//�����
	DeleteFile( m_strUpdateF.GetBuffer(0) );
	DeleteFile( m_strUpdateH.GetBuffer(0) );
	DeleteFile( m_strUpdateL.GetBuffer(0) );

	FILE_CLOSE( m_fhS );
	FILE_CLOSE( m_fhU );
	FILE_CLOSE( m_fhSH );
	FILE_CLOSE( m_fhUH );

	DeleteFileList( m_saDirS ); 
	m_saDirS = NULL;
	
	{
	std::list<UpdateFile*>::iterator iter = m_list.m_updateL.begin();
	for( ; iter != m_list.m_updateL.end(); iter++ )
	{
		UpdateFile *uf = *iter;
		SAFE_DELETE( uf );
	}
	m_list.m_updateL.clear();
	}
	
	DeleteEmptyList();

}

void SfileUpdateDlg::ReadSAFile( int &fh, SADir *saDir )
{
	//�����̸� 
	int strSize;
	_read( fh, &strSize, sizeof(int) );
	_read( fh, saDir->szName, strSize );

	int nCnt;
	SAFile *saFile;

	//����
	{
	_read( fh, &nCnt, sizeof(int) );
	for( int i=0; i < nCnt; i++ )
	{
		saFile = new SAFile;
		//szName
		_read( fh, &strSize, sizeof(int) );
		_read( fh, saFile->szName, strSize );
		_read( fh, &saFile->offset, sizeof(__int64) );

		//size
		_read( fh, &saFile->size, sizeof(unsigned long) );
		//time
		_read( fh, &saFile->time, sizeof(long) );

		saDir->fileL.push_back( saFile );
	}
	}

	//���丮 
	{
	_read( fh, &nCnt, sizeof(int) );
	for( int i=0; i < nCnt; i++ )
	{
		SADir *dir = new SADir;
		saDir->dirL.push_back(dir);

		ReadSAFile( fh, dir );
	}
	}

}


void SfileUpdateDlg::ReadGarbageSize( int &fh )
{	
	m_dwGarbageSize = 0;
	_read( fh, &m_dwGarbageSize, sizeof(DWORD) );
}

void SfileUpdateDlg::MakeUpdateFileList()
{
	std::list<UpdateFile*>::iterator iter;

	int index = 0;
	
	for( iter = m_list.m_updateL.begin(); iter != m_list.m_updateL.end(); iter++ )
	{
		//���� ��Ͽ��� �������� ���� empty����Ʈ�� 
		UpdateFile *uf = *iter;
		DeleteSrcFileList( uf->szName + m_list.m_strRootDir.GetLength() );
	}
}

void SfileUpdateDlg::DeleteSrcFileList( char *filename )
{
	char buf[MAX_FILE_PATH];
	strcpy( buf, filename );

	char *str;
	int i=0, j = 0, len=0;

	SADir *dir = m_saDirS;
	SADir *child;
	SADIter dIter;
	SAFIter fIter;
	SAFile *file;

	bool flg;

	while(1)
	{
		if( buf[i] == '/' || buf[i] == '\\' )
		{
			str = &buf[j];
			
			flg = false;
			for( dIter = dir->dirL.begin(); dIter != dir->dirL.end(); dIter++ )
			{
				child = *dIter;
				if( strnicmp( str, child->szName , len ) == 0 )
				{
					dir = child;
					flg = true;
					break;
				}
			}

			if( flg == false ) return;
			
			i++;
			j = i;
			len = 0;
			continue;
		}
		else
		if( buf[i] == NULL )
		{
			str = &buf[j];

			flg = false;
			for( fIter = dir->fileL.begin(); fIter != dir->fileL.end(); fIter++ )
			{
				file = *fIter;
				if( strnicmp( str, file->szName , len ) == 0 )
				{
					if( file->size != 0 )
					{
						m_emptyL.push_back(file);
					}
					
					dir->fileL.erase( fIter );

					m_nSrcFileCnt--;
					return;
				}
			}

			if( flg == false ) return;

		}
	
		i++;
		len++;
	}
}

bool SfileUpdateDlg::CopyUpdateFile()
{
	CString str;
	__int64 offset;
	std::list<UpdateFile*>::iterator iter = m_list.m_updateL.begin();
	for( ; iter != m_list.m_updateL.end(); iter++ )
	{
		UpdateFile *uf = *iter;
	
		if( ChkEmptyList( uf->file->size, offset ) )
		{
			if( !WriteFile( uf, offset ) )
				return false;
		}
		else
		{
			if( !AppendFile( uf ) )
				return false;
		}

		UpdateSrcFileList( uf->szName+m_list.m_strRootDir.GetLength(), uf->file );
	}

	return true;
}

bool SfileUpdateDlg::ChkEmptyList( unsigned int size, __int64 &offset )
{
	SAFile *file;
	unsigned int freeSize;
	std::list<SAFile*>::iterator iter = m_emptyL.begin();
	for( ; iter != m_emptyL.end(); iter++ )
	{
		file = *iter;
		if( file->size >= size )
		{
			offset = file->offset;

			//����ϰ� ���� �뷮�� empty list�� 
			//���ϳ��� NULL�� �߰��ǹǷ� ����
			if( file->size - size > 1 )
			{
				freeSize = file->size - (size+1);

				SAFile *f = new SAFile;
				strcpy( f->szName, "���� ��������" );
				f->size = freeSize;
				f->offset = file->offset + (__int64)(size+1);
				
				//���� �ϸ� ������� ���� 
				m_emptyL.push_back(f);
			}

			delete file;
			m_emptyL.erase(iter);

			return true;
		}
	}

	return false;
}

bool SfileUpdateDlg::WriteFile( UpdateFile *uf, __int64 offset )
{
	FILE_CLOSE( m_fhS )
	m_fhS = _open( m_strSrcF, _O_RDWR | _O_BINARY ); 
	if( m_fhS == -1 ) return false;
	_lseeki64( m_fhS, offset, SEEK_SET );

	SAFile *file = uf->file;

	FILE_CLOSE( m_fhU )
	m_fhU = _open( uf->szName, _O_RDONLY | _O_BINARY );
	if( m_fhU == -1 ) return false;

	unsigned long size = file->size, wSize, numSize = 0;

	while(1)
	{
		if( size > BUFSIZE ) wSize = BUFSIZE;
		else wSize = size;

		_read( m_fhU, g_buf, wSize );
		_write( m_fhS, g_buf, wSize );
		
		numSize += wSize;
		size -= wSize;
		
		if( size <= 0) break;
	}
	//���ϳ��� NULL�� �߰���
	_write( m_fhS, "\0", 1 );
	
	file->offset = offset;

	m_nProgressCnt++;
	m_prgress.SetPos( m_nProgressCnt );	

	return true;
}

bool SfileUpdateDlg::AppendFile( UpdateFile *uf )
{
	FILE_CLOSE( m_fhS )
	m_fhS = _open( m_strSrcF, _O_RDWR | _O_APPEND | _O_BINARY );
	if( m_fhS == -1 ) return false;
	_lseeki64( m_fhS, 0, SEEK_END );

	SAFile *file = uf->file;

	FILE_CLOSE( m_fhU )
	m_fhU = _open( uf->szName, _O_RDONLY | _O_BINARY );
	if( m_fhU == -1 ) return false;

	unsigned long size = file->size, wSize, numSize = 0;
	__int64 offset = _telli64( m_fhS );
	while(1)
	{
		if( size > BUFSIZE ) wSize = BUFSIZE;
		else wSize = size;

		_read( m_fhU, g_buf, wSize );
		_write( m_fhS, g_buf, wSize );
		
		numSize += wSize;
		size -= wSize;
		
		if( size <= 0) break;
	}
	//���ϳ��� NULL�� �߰���
	_write( m_fhS, "\0", 1 );

	file->offset = offset;

	m_nProgressCnt++;
	m_prgress.SetPos( m_nProgressCnt );	

	return true;
}

void SfileUpdateDlg::UpdateSrcFileList( char *filename, SAFile *file )
{
	CString tmp;
	char buf[1000];

	tmp = filename;

	SADir *pDir = m_saDirS;
	SADIter iter;

	int len = 0;
	int nPos = 1;
	while((nPos = tmp.Find('\\', nPos+1)) != -1) 
	{
		strcpy(buf, tmp.Left(nPos).GetBuffer(0) + len );
				
		bool flg = false;
		for( iter = pDir->dirL.begin(); iter != pDir->dirL.end(); iter++ )
		{
			SADir *d = (SADir*)(*iter);
			
			if( stricmp( buf, d->szName ) == 0 )
			{
				pDir = d;
				flg = true;
				break;
			}
		}

		if( flg == false )
		{
			SADir *d = new SADir;
			strcpy( d->szName, buf ); 
			pDir->dirL.push_back( d );
			pDir = d;
		}

		len += strlen(buf) + 1;
	}

	pDir->fileL.push_back( file );

}

//������ 64��Ʈ offset���� ����� 
void SfileUpdateDlg::SaveHeader( SADir *dir )
{
	int strSize;
	SAFile *f;
	SADir *d;

	//�����̸� 
	strSize = strlen( dir->szName ) + 1;
	_write( m_fhSH, &strSize, sizeof(int) );
	_write( m_fhSH, dir->szName, strSize );
	

	//���� 
	{
	int size = dir->fileL.size();
	_write( m_fhSH, &size, sizeof(int) );

	SAFIter iter;
	for( iter = dir->fileL.begin(); iter != dir->fileL.end(); iter++ )
	{
		f = (SAFile*)*iter;
		
		//szName
		strSize = strlen( f->szName ) + 1;
		_write( m_fhSH, &strSize, sizeof(int) );
		_write( m_fhSH, f->szName, strSize );
		
		//offset
		_write( m_fhSH, &f->offset, sizeof(__int64) );
		//size
		_write( m_fhSH, &f->size, sizeof(unsigned long) );
		//time
		_write( m_fhSH, &f->time, sizeof(long) );
	}
	}

	//���丮
	{
	int size = dir->dirL.size();
	_write( m_fhSH, &size, sizeof(int) );

	SADIter iter;
	for( iter = dir->dirL.begin(); iter != dir->dirL.end(); iter++ )
	{
		d = (SADir*)*iter;
		SaveHeader( d );
	}
	}
}

//������ 64��Ʈ offset���� ����� 
void SfileUpdateDlg::SaveHeaderEmptyList( int &fh )
{
	unsigned int cnt;
	int strSize;
	SAFile *file;
	cnt = m_emptyL.size();

	_write( fh, &cnt, sizeof(unsigned int) );
	std::list<SAFile*>::iterator iter;

	for( iter = m_emptyL.begin(); iter != m_emptyL.end(); iter++ )
	{
		file = *iter;	

		//szName
		strSize = strlen( file->szName ) + 1;
		_write( fh, &strSize, sizeof(int) );
		_write( fh, file->szName, strSize );
		
		//offset
		_write( fh, &file->offset, sizeof(__int64) );
		//size
		_write( fh, &file->size, sizeof(unsigned long) );
		//time
		_write( fh, &file->time, sizeof(long) );
	}
}

void SfileUpdateDlg::SaveHeaderGarbageSize( int &fh )
{
	//������ ������ �뷮 
	_write( fh, &m_dwGarbageSize, sizeof(DWORD) );
}

void SfileUpdateDlg::DeleteEmptyList()
{
	{
	std::list<SAFile*>::iterator iter = m_emptyL.begin();
	for( ; iter != m_emptyL.end(); iter++ )
	{
		delete *iter;
	}
	m_emptyL.clear();
	}

}

void SfileUpdateDlg::OnOK() 
{
	// TODO: Add extra validation here
	m_list.ReleaseList();	

	CDialog::OnOK();
}

void SfileUpdateDlg::SetDlgItemEnable( BOOL flg )
{
	GetDlgItem( IDC_BUTTON_SAFILE )->EnableWindow( flg );
	GetDlgItem( IDC_EDIT_ROOT )->EnableWindow( flg );
	GetDlgItem( IDC_BUTTON_ROOT )->EnableWindow( flg );
	GetDlgItem( IDC_LIST_FILES )->EnableWindow( flg );
	GetDlgItem( IDC_BUTTON_START )->EnableWindow( flg );
	GetDlgItem( IDOK )->EnableWindow( flg );
}

void SfileUpdateDlg::AutoAddDataFolder( char *dataFolder )
{
	// 自动添加数据文件夹中的所有文件到更新列表
	m_list.m_strRootDir = dataFolder;

	// 确保路径以反斜杠结尾
	if( m_list.m_strRootDir.Right(1) != "\\" )
	{
		m_list.m_strRootDir += "\\";
	}

	// 递归添加文件夹中的所有文件
	m_list.AddFolderRecursive( dataFolder );
}

void SfileUpdateDlg::AutoStart()
{
	// 自动开始处理（模拟点击开始按钮）
	PostMessage( WM_COMMAND, MAKEWPARAM(IDC_BUTTON_START, BN_CLICKED), 0 );
}
