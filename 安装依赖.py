#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装运维GUI工具所需的依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"❌ {package} 未安装")
        return False

def main():
    print("=" * 50)
    print("运维GUI工具依赖安装脚本")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "rarfile",  # 用于处理RAR文件
    ]
    
    print("\n检查依赖包...")
    
    need_install = []
    for package in packages:
        if not check_package(package):
            need_install.append(package)
    
    if not need_install:
        print("\n🎉 所有依赖包都已安装!")
        return
    
    print(f"\n需要安装的包: {', '.join(need_install)}")
    
    # 安装缺失的包
    failed_packages = []
    for package in need_install:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("\n请手动安装:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("\n🎉 所有依赖包安装完成!")
    
    print("\n注意事项:")
    print("1. 如果需要处理RAR文件，还需要安装WinRAR或7-Zip")
    print("2. 确保SFile.exe和Themida.exe在工作目录下")
    print("3. 确保launcher.shaiya配置文件存在")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
