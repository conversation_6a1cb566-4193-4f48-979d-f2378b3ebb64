1.运营的包(客户端文件(client文件夹下有data文件夹))
    data文件夹需要使用SFile.exe打包
    打包完生成一个文件夹~patch：
        update.saf
        update.sah
    我们需要创建一个文件夹为patch~,然后把这两个文件：update.saf、update.sah放进去。

2.技术的包:
    Client game.exe 
    Server p_game.exe p_dbAgent.exe

    我们会使用Themida.exe对game.exe进行加壳，它会生成一个game_protected.exe，
    现在可以删除game.exe，
    然后将game_protected.exe重命名为game.exe，
    将这个game.exe发送给sp小组。他们会给我们签名，生成文件名为game(签名).exe，将该文件放入到patch~文件夹下。然后将game(签名).exe改名为game.exe，然后对patch~文件夹进行zip压缩，然后打开MD5.exe，将patch~.zip拖入MD5中，生成MD5值，然后打开launcher.shaiya文件，修改[Patch]下的Version，使其+1：
    例如:
    Version=0x010000d5
    则修改为
    Version=0x010000d6

    然后在[Patch]下追加一行：
    0x010000d6=MD5值
    例如：
    0x010000d6=9d3e00816ba0122093fb169b371f17f4

    然后将patch~.zip改名为
    刚才生成的MD5值.bin



