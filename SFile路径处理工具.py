#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFile路径处理工具
解决中文路径问题
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path
import tempfile

class SFilePathProcessor:
    def __init__(self):
        self.work_dir = Path.cwd()
        self.temp_dir = None
        
    def log(self, message):
        print(f"[INFO] {message}")
        
    def error(self, message):
        print(f"[ERROR] {message}")
        
    def has_chinese_chars(self, path_str):
        """检查路径是否包含中文字符"""
        return any('\u4e00' <= char <= '\u9fff' for char in path_str)
        
    def create_english_temp_dir(self):
        """创建英文临时目录"""
        # 在当前目录下创建临时英文目录
        temp_base = self.work_dir / "temp_sfile_work"
        if temp_base.exists():
            shutil.rmtree(temp_base)
        temp_base.mkdir()
        
        self.temp_dir = temp_base
        self.log(f"创建临时工作目录: {self.temp_dir}")
        return self.temp_dir
        
    def copy_data_to_temp(self, source_data_path):
        """复制data文件夹到临时目录"""
        source_path = Path(source_data_path)
        if not source_path.exists():
            self.error(f"源路径不存在: {source_path}")
            return None
            
        if not self.temp_dir:
            self.create_english_temp_dir()
            
        # 复制到临时目录
        temp_data_path = self.temp_dir / "data"
        
        try:
            if source_path.is_dir():
                shutil.copytree(source_path, temp_data_path)
            else:
                self.error(f"路径不是文件夹: {source_path}")
                return None
                
            self.log(f"已复制数据到: {temp_data_path}")
            return temp_data_path
            
        except Exception as e:
            self.error(f"复制文件失败: {e}")
            return None
            
    def run_sfile(self, data_path):
        """运行SFile.exe处理数据"""
        sfile_exe = self.work_dir / "SFile.exe"
        sfile_new = self.work_dir / "SFile_new.exe"
        
        # 选择SFile版本
        if sfile_new.exists():
            sfile_path = sfile_new
            self.log("使用修改后的SFile_new.exe")
        elif sfile_exe.exists():
            sfile_path = sfile_exe
            self.log("使用原版SFile.exe")
        else:
            self.error("未找到SFile.exe")
            return False
            
        try:
            # 调用SFile.exe
            cmd = [str(sfile_path), "-make", str(data_path)]
            self.log(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.work_dir,
                timeout=300
            )
            
            self.log(f"SFile.exe返回码: {result.returncode}")
            
            if result.stdout:
                self.log(f"输出: {result.stdout}")
            if result.stderr:
                self.log(f"错误: {result.stderr}")
                
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            self.error("SFile.exe执行超时")
            return False
        except Exception as e:
            self.error(f"执行SFile.exe失败: {e}")
            return False
            
    def check_output_files(self):
        """检查生成的输出文件"""
        update_saf = self.work_dir / "update.saf"
        update_sah = self.work_dir / "update.sah"
        
        files_found = []
        
        if update_saf.exists():
            size = update_saf.stat().st_size
            files_found.append(f"update.saf ({size} bytes)")
            
        if update_sah.exists():
            size = update_sah.stat().st_size
            files_found.append(f"update.sah ({size} bytes)")
            
        if files_found:
            self.log(f"✅ 找到生成的文件: {', '.join(files_found)}")
            return True
        else:
            self.error("❌ 未找到生成的文件")
            return False
            
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
                self.log(f"已清理临时目录: {self.temp_dir}")
            except Exception as e:
                self.error(f"清理临时目录失败: {e}")
                
    def process_data_folder(self, data_folder_path):
        """处理data文件夹"""
        self.log("=" * 60)
        self.log("SFile路径处理工具")
        self.log("=" * 60)
        
        data_path = Path(data_folder_path)
        
        # 检查路径是否存在
        if not data_path.exists():
            self.error(f"路径不存在: {data_path}")
            return False
            
        # 检查是否包含中文字符
        path_str = str(data_path)
        if self.has_chinese_chars(path_str):
            self.log("⚠️  检测到中文路径，将复制到英文临时目录")
            
            # 复制到临时目录
            temp_data_path = self.copy_data_to_temp(data_path)
            if not temp_data_path:
                return False
                
            process_path = temp_data_path
        else:
            self.log("✅ 路径为英文，直接处理")
            process_path = data_path
            
        # 运行SFile.exe
        self.log(f"开始处理: {process_path}")
        success = self.run_sfile(process_path)
        
        if success:
            # 检查输出文件
            if self.check_output_files():
                self.log("🎉 处理完成！")
                return True
            else:
                self.error("处理失败：未生成预期文件")
                return False
        else:
            self.error("SFile.exe执行失败")
            return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("python SFile路径处理工具.py \"data文件夹路径\"")
        print("")
        print("示例:")
        print("python SFile路径处理工具.py \"F:\\维护\\数据\\20250408\\运营\\1.0.0.213(1)\\Client\\data\"")
        return
        
    data_folder = sys.argv[1]
    
    processor = SFilePathProcessor()
    
    try:
        success = processor.process_data_folder(data_folder)
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 处理成功！")
            print("生成的文件:")
            print("- update.saf")
            print("- update.sah")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ 处理失败！")
            print("请检查:")
            print("1. 路径是否正确")
            print("2. 文件夹是否包含有效数据")
            print("3. SFile.exe是否存在")
            print("=" * 60)
            
    finally:
        processor.cleanup()

if __name__ == "__main__":
    main()
