#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修改的简单脚本
"""

import sys
import traceback

def test_import():
    """测试导入"""
    try:
        print("测试导入模块...")
        import tkinter as tk
        from tkinter import ttk
        import json
        from pathlib import Path
        print("✅ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    try:
        print("测试GUI创建...")
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 测试基本控件
        frame = ttk.Frame(root)
        frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        label = ttk.Label(frame, text="测试标签")
        label.pack(pady=5)
        
        listbox = tk.Listbox(frame, selectmode=tk.EXTENDED)
        listbox.pack(fill="both", expand=True, pady=5)
        
        # 添加测试项目
        listbox.insert(tk.END, "📁 测试文件夹1")
        listbox.insert(tk.END, "📦 测试压缩包.zip")
        listbox.insert(tk.END, "📁 测试文件夹2")
        
        button = ttk.Button(frame, text="关闭", command=root.destroy)
        button.pack(pady=5)
        
        print("✅ GUI创建成功")
        print("显示测试窗口3秒...")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        traceback.print_exc()
        return False

def test_config_file():
    """测试配置文件功能"""
    try:
        print("测试配置文件功能...")
        import json
        from pathlib import Path
        
        # 测试配置文件读写
        config_file = Path("test_config.json")
        
        # 写入测试配置
        test_config = {
            "data_path": "C:\\test_path",
            "test_value": "测试值"
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        # 读取配置
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        # 验证
        if loaded_config == test_config:
            print("✅ 配置文件读写成功")
            
            # 清理测试文件
            config_file.unlink()
            return True
        else:
            print("❌ 配置文件内容不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("GUI修改测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_import),
        ("配置文件", test_config_file),
        ("GUI创建", test_gui_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！GUI修改应该正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查环境配置。")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
