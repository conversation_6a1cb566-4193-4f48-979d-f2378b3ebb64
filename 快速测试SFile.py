#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试SFile中文路径处理
"""

import subprocess
import sys
from pathlib import Path

def test_sfile_with_path(data_path):
    """测试SFile处理指定路径"""
    print("=" * 60)
    print("快速测试SFile中文路径处理")
    print("=" * 60)
    
    # 检查路径
    data_folder = Path(data_path)
    if not data_folder.exists():
        print(f"❌ 路径不存在: {data_folder}")
        return False
        
    print(f"📁 测试路径: {data_folder}")
    
    # 检查是否包含中文
    path_str = str(data_folder)
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in path_str)
    
    if has_chinese:
        print("⚠️  检测到中文路径")
    else:
        print("✅ 英文路径")
    
    # 检查SFile.exe
    current_dir = Path.cwd()
    sfile_exe = current_dir / "SFile.exe"
    sfile_new = current_dir / "SFile_new.exe"
    
    if sfile_new.exists():
        sfile_path = sfile_new
        print(f"🔧 使用修改版: {sfile_path}")
    elif sfile_exe.exists():
        sfile_path = sfile_exe
        print(f"📦 使用原版: {sfile_path}")
    else:
        print("❌ 未找到SFile.exe")
        return False
    
    # 方案1: 直接测试命令行
    print("\n🧪 方案1: 直接命令行测试")
    try:
        cmd = [str(sfile_path), "-make", str(data_folder)]
        print(f"执行: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=current_dir,
            timeout=60
        )
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
            
    except Exception as e:
        print(f"执行失败: {e}")
    
    # 方案2: 使用路径处理工具
    print("\n🛠️  方案2: 使用路径处理工具")
    try:
        path_tool = current_dir / "SFile路径处理工具.py"
        if path_tool.exists():
            cmd = [sys.executable, str(path_tool), str(data_folder)]
            print(f"执行: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=current_dir,
                timeout=120
            )
            
            print(f"路径处理工具返回码: {result.returncode}")
        else:
            print("❌ 未找到SFile路径处理工具.py")
            
    except Exception as e:
        print(f"路径处理工具执行失败: {e}")
    
    # 检查结果
    print("\n📋 检查生成的文件:")
    update_saf = current_dir / "update.saf"
    update_sah = current_dir / "update.sah"
    
    success = False
    
    if update_saf.exists():
        size = update_saf.stat().st_size
        print(f"✅ update.saf - {size} bytes")
        success = True
    else:
        print("❌ update.saf - 未生成")
    
    if update_sah.exists():
        size = update_sah.stat().st_size
        print(f"✅ update.sah - {size} bytes")
        success = True
    else:
        print("❌ update.sah - 未生成")
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！")
        if update_saf.exists() and update_sah.exists():
            print("✅ 生成了完整的.saf和.sah文件")
        else:
            print("⚠️  只生成了部分文件")
    else:
        print("❌ 测试失败")
        print("建议:")
        print("1. 检查SFile.exe是否为修改后的版本")
        print("2. 尝试使用英文路径")
        print("3. 检查文件夹权限")
    print("=" * 60)
    
    return success

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("python 快速测试SFile.py \"data文件夹路径\"")
        print("")
        print("示例:")
        print("python 快速测试SFile.py \"F:\\维护\\数据\\Client\\data\"")
        return
        
    data_path = sys.argv[1]
    test_sfile_with_path(data_path)

if __name__ == "__main__":
    main()
