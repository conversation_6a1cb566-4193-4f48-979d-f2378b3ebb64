// SfileDlg.cpp : implementation file
//

#include "stdafx.h"
#include "SFile.h"
#include "SAFile.h"
#include "SfileDlg.h"
#include <sys/stat.h>
#include <stdlib.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

//���� Ȯ����
char FILEEXT[1024];
char TARGETDIR[2048];
char OUTFILEPATH[2048];
char OUTFILENAME[1024];
char OUTFILEHEADERNAME[1024];

/////////////////////////////////////////////////////////////////////////////
// SfileDlg dialog


SfileDlg::SfileDlg(CWnd* pParent /*=NULL*/)
	: CDialog(SfileDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(SfileDlg)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void SfileDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(SfileDlg)
	DDX_Control(pDX, IDC_SPIN_VER, m_spinVer);
	//}}AFX_DATA_MAP

	CButton* pRadio = (CButton*)GetDlgItem( IDC_RADIO2 );
	pRadio->SetCheck(1);
	SetDlgItemText( IDC_EDIT_TARGET, "update" );	
}


BEGIN_MESSAGE_MAP(SfileDlg, CDialog)
	//{{AFX_MSG_MAP(SfileDlg)
	ON_BN_CLICKED(IDC_BUTTON_FILE_ADD, OnButtonFileAdd)
	ON_WM_CLOSE()
	ON_BN_CLICKED(IDC_BUTTON_DIR, OnButtonDir)
	ON_BN_CLICKED(IDC_RADIO1, OnRadio1)
	ON_BN_CLICKED(IDC_RADIO2, OnRadio2)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// SfileDlg message handlers

DWORD WINAPI ThreadMakeDataFile( void far* para )
{
	SfileDlg *dlg = (SfileDlg*)para;

	//----------------------------------------------
	//����Ÿ ���� 
	CString path;
	path = TARGETDIR;

	//
	dlg->SearchFile(path);
	
	char buf[128];
	dlg->m_nFileSize = _telli64( dlg->m_fhW );
	_i64toa( dlg->m_nFileSize, buf, 10 );
	dlg->SetDlgItemText( IDC_STATIC_SIZE, buf );

	_close( dlg->m_fhW );
	dlg->m_fhW = -1;
	//----------------------------------------------

	//----------------------------------------------
	//��� ���� 
#ifdef __SAVE_NEWTYPE__
	char *id = "SAH";
	_write( dlg->m_fhWH, id, sizeof(char)*3 );
	_write( dlg->m_fhWH, &dlg->m_nVer, sizeof(unsigned int) );
	_write( dlg->m_fhWH, &dlg->m_nFileCnt, sizeof(unsigned int) );
	unsigned int tmp[10]; //40 byte ���� (���߿� �ʿ信 ���� ����)
	memset( tmp, NULL, sizeof(tmp) );
	_write( dlg->m_fhWH, tmp, sizeof(unsigned int)*10 );
#else
	//����,���ϼ� 
	_write( dlg->m_fhWH, &dlg->m_nVer, sizeof(unsigned int) );
	_write( dlg->m_fhWH, &dlg->m_nFileCnt, sizeof(unsigned int) );
#endif

	dlg->SaveHeader( dlg->m_saDir );


	unsigned int numEmpty = 0;
	_write( dlg->m_fhWH, &numEmpty, sizeof(unsigned int) );
	
	_close( dlg->m_fhWH );
	dlg->m_fhWH = -1;
	//----------------------------------------------

	CString msg;
	msg = msg + "�Ϸ�!\n" + 
		  OUTFILEPATH + "\\" + OUTFILENAME + '\n' +
		  OUTFILEPATH + "\\" + OUTFILEHEADERNAME + '\n';

	AfxMessageBox( msg.GetBuffer(0) );
	dlg->SendMessage( WM_CLOSE );


	return 0;
}

void SfileDlg::OnButtonFileAdd() 
{
	SetControl(FALSE);

	m_nVer = GetDlgItemInt( IDC_EDIT_VER, NULL, FALSE );

	CString path;
	GetDlgItemText( IDC_EDIT_DIR, path );
	
	if( path.IsEmpty() )
	{
		AfxMessageBox("���丮�� ����ֽ��ϴ�.");
		SetControl(TRUE);
		return;
	}

	strcpy( TARGETDIR, path.GetBuffer(0) );
	
	if( _access( path.GetBuffer(0), 0 ) == -1 )
	{
		AfxMessageBox("���丮�� �������� �ʽ��ϴ�.");
		SetControl(TRUE);
		return;
	}

	//Ȯ����
	CString ext;
	GetDlgItemText( IDC_EDIT_EXT, ext);

	if( ext.IsEmpty() )
	{
		ext = "*.*";
	}

	strcpy( FILEEXT, ext.GetBuffer(0) );


	//Ÿ������
	CString target;
	GetDlgItemText( IDC_EDIT_TARGET, target );

	if( target == CString( "data" ) )
	{
		int res = MessageBox( "data�� ���õǾ��ֽ��ϴ�. �̱���� ǮŬ���̾�Ʈ ��ŷ�Ҷ� ���Ǵ� ����Դϴ�. ����Ͻðڽ��ϱ�?", "���!!", MB_YESNO );

		if( res == IDNO )
		{
			SetControl(TRUE);
			return;
		}

	}
	
	if( target.IsEmpty() )
	{
		AfxMessageBox("�������� �̸��� ����ֽ��ϴ�.");
		SetControl(TRUE);
		return;
	}
	strcpy( OUTFILENAME, target.GetBuffer(0) );	
	
	//�ϴ� Ȯ���� ���� 
	int index = strlen(OUTFILENAME) -1;
	while(1)
	{
		if( OUTFILENAME[index] == '.' )
		{
			OUTFILENAME[index] = NULL;
			break;
		}

		index--;
		if( index < 0 )
			break;
	}

	strcpy( OUTFILEHEADERNAME, OUTFILENAME );
	strcat( OUTFILENAME, ".saf" );
	strcat( OUTFILEHEADERNAME, ".sah");


	//����� ������ �ִ��� Ȯ�� 
	//..

	CString strWpath;
	char szWpath[1024];
	strcpy( szWpath, path.GetBuffer(0) );
	int i = strlen(szWpath)-1;
	while(1)
	{
		if( i < 0 || szWpath[i] == NULL || szWpath[i] == '\\' || szWpath[i] == ':' )
			break;
		i--;
	}
	
	if( szWpath[i] == '\\' ) szWpath[i] = NULL;
	strcpy( OUTFILEPATH, szWpath );

	CString strFileName;
	strFileName = strFileName + OUTFILEPATH + "\\" + OUTFILENAME; 
	m_fhW = _open( strFileName.GetBuffer(0), _O_WRONLY | _O_TRUNC | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );
	//m_fhW = _open( strFileName.GetBuffer(0), _O_WRONLY | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );

	
	if( m_fhW == -1 )
	{
		CString str;
		str.Format("%s�� ����Ÿ ������ �����Ҽ� �����ϴ�.", path );
		AfxMessageBox( str.GetBuffer(0) );
		SetControl(TRUE);
		return;
	}

	strFileName.Empty();
	strFileName = strFileName + OUTFILEPATH + "\\" + OUTFILEHEADERNAME; 
	m_fhWH = _open( strFileName.GetBuffer(0), _O_WRONLY | _O_TRUNC | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );
	//m_fhWH = _open( strFileName.GetBuffer(0), _O_WRONLY | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE );

	if( m_fhWH == -1 )
	{
		CString str;
		str.Format("%s�� ��� ������ �����Ҽ� �����ϴ�.", path );
		AfxMessageBox( str.GetBuffer(0) );
		SetControl(TRUE);
		return;
	}

	DWORD threadID = 0;
	::CreateThread( 0, 0, ThreadMakeDataFile, this, 0, &threadID );
}


BOOL SfileDlg::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{
	// TODO: Add your specialized code here and/or call the base class
		
	return CDialog::Create(IDD, pParentWnd);
}

BOOL SfileDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	m_saDir = new SADir;
	memset( m_saDir->szName, NULL, sizeof(m_saDir->szName) );
	m_fhW = -1;
	m_fhWH = -1;
	m_nFileCnt = 0;
	m_nDirCnt = 1;
	m_nFileSize = 0;
	m_lOffset = 0;

	char path[MAX_PATH];
    GetCurrentDirectory(MAX_PATH, path);
	strcat(path,"\\~patch\\data");
	SetDlgItemText( IDC_EDIT_DIR, path );
	SetDlgItemText( IDC_EDIT_EXT, "*.*" );
	m_spinVer.SetRange32(0, 1000000 );

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void SfileDlg::SearchFile(CString strName)
{
	Sleep(100);
	CString path;
	path = strName + "\\*.*";

	CFileFind finder;
	BOOL bWorking = finder.FindFile(path.GetBuffer(0));

	while (bWorking)
	{
		bWorking = finder.FindNextFile();

		//directory
		if(finder.IsDirectory())
		{
			if(!finder.IsDots())
			{
				//���� ���丮 
				CString folder = strName + '\\' + finder.GetFileName();
				SearchFile( folder );
			}
		}
		//file
		else
		{
			AddFile( strName, finder.GetFileName() );
		}
	}

	finder.Close();

}

time_t SfileDlg::GetTime( CString &fileName )
{
	CFileStatus status; 
	CFile::GetStatus( fileName, status );
	
	return status.m_mtime.GetTime();
}



void SfileDlg::AddFile(CString path, CString name)
{
	CString t;
	t = path + "\\" + name;

	// 修复路径计算问题：正确计算相对路径
	CString relativePath;
	CString targetDir = TARGETDIR;

	// 确保targetDir不以反斜杠结尾（应该在SFile.cpp中已经处理了）
	if (targetDir.Right(1) == "\\") {
		targetDir = targetDir.Left(targetDir.GetLength() - 1);
	}

	// 计算相对路径
	CString targetDirWithSlash = targetDir + "\\";
	if (t.GetLength() > targetDirWithSlash.GetLength() &&
		t.Left(targetDirWithSlash.GetLength()).CompareNoCase(targetDirWithSlash) == 0) {
		// 文件路径以目标目录开头，计算相对路径
		relativePath = t.Mid(targetDirWithSlash.GetLength());
	} else {
		// 如果路径不匹配，只显示文件名
		relativePath = name;
	}

	SetDlgItemText( IDC_STATIC_WORK_FILE, relativePath.GetBuffer(0) );

	//���� Ȯ���� �˻�
	//if( ChkFileExt( name ) == false ) return;

	CString fullPath;
	fullPath = path + "\\" + name;

	CFile file;
	if( !file.Open( fullPath, CFile::modeRead ) ) return;

	unsigned char *buf = NULL;
	
	buf = new unsigned char[file.GetLength()];
	if( buf == NULL ) 
	{
		AfxMessageBox( "file Add <new> Err!!" );
		return;
	}

	DWORD dwByte;
	dwByte = file.Read( (void*)buf, file.GetLength() );
	if( dwByte != file.GetLength() )
	{
		AfxMessageBox( "file read length Err!!" );
	}

	dwByte = _write( m_fhW, buf, file.GetLength() );
	if( dwByte != file.GetLength() )
	{
		AfxMessageBox( "file write length Err!!" );
	}
	
	//���ϰ� ���ϻ��̿� NULL�� �ִ´�.
	_write( m_fhW, "\0", 1 );	
	
	delete buf;

	AddFileList( path, name, file.GetLength(), (long)GetTime( fullPath ) );

	m_lOffset += (__int64)file.GetLength() + 1;
	m_nFileSize += (__int64)file.GetLength();
	m_nFileCnt++;
	{
		char buf[128];
		_i64toa( m_nFileSize, buf, 10 );
		SetDlgItemText( IDC_STATIC_SIZE, buf );
	}
	SetDlgItemInt( IDC_STATIC_FILE_CNT, m_nFileCnt, FALSE );
}

bool SfileDlg::ChkFileExt(const char *fileName)
{
	char ext[128];
	char buf[128];
	
	memset(buf, NULL, sizeof(buf));

	//���� Ȯ����
    char *tmp = NULL;
	bool flgExt = false;

	strcpy(buf, fileName);
	int cnt = (int)strlen(buf);
	
	if( cnt > 0 )
	{
		tmp = &buf[cnt-1];
		while( *tmp != '\\' )
		{
			if( *tmp == '.' )
			{
				if(cnt != (int)strlen(buf) ) flgExt = true;
				break;
			}
			
			cnt--;

			if(cnt < 1) 
				break;

			tmp = &(buf[cnt-1]);
		}
	}
	

	if( flgExt == false ) 
		ext[0] = NULL;
	else
		strcpy(ext, tmp+1);

	//��
	if( strlen(FILEEXT) == 0 || strstr(FILEEXT, "*.*") )
		return true;

	int extsz = (int)strlen(ext);
	CString _ext = FILEEXT;
	int index = -1;
	
	while(1)
	{
		index = _ext.Find(ext, index +1);
		if( index == -1 ) break;

		//Ȯ���� ���ķ� ���ڰ� �ִٸ� ������ ��ġ���� �ʴ°��̴�.
		if( ( index == 0 || FILEEXT[index -1] == NULL || FILEEXT[index -1] == ' ') &&
			( FILEEXT[index + extsz] == NULL || FILEEXT[index + extsz] == ' ')    )
			return true;
	}

	return false;
}

void SfileDlg::OnClose() 
{
	// TODO: Add your message handler code here and/or call default
	Release();
	CDialog::OnClose();
}


void SfileDlg::Release()
{
	//����Ʈ ����� 
	DeleteFileList( m_saDir );
	m_saDir = NULL;

	//���ϴݱ� 
	if( m_fhW != -1 )
	{
		_close( m_fhW );
		m_fhW = -1;
	}

	if( m_fhWH != -1 )
	{
		_close( m_fhWH );
		m_fhWH = -1;
	}

}

void SfileDlg::DeleteFileList(SADir *dir)
{
	if( dir	)
	{
		//child
		SADIter i = dir->dirL.begin();
		for( ; i != dir->dirL.end(); i++ )
		{
			DeleteFileList( (SADir*)*i);
		}

		SAFIter j = dir->fileL.begin();
		for( ; j != dir->fileL.end(); j++ )
		{
			delete *j;
		}

		delete dir;
	}
}

void SfileDlg::AddFileList(CString path, CString name, unsigned long length, long time)
{
	CString tmp;
	char buf[1000];
	//char *szDir = path.GetBuffer(0);
	char szDir[1000];
	strcpy( szDir, path.GetBuffer(0) );
	
	if( path.GetLength() > strlen(TARGETDIR)+1 )
	{
		tmp = &szDir[strlen(TARGETDIR) +1];
		tmp = tmp + "\\";
	}
	else
		tmp = "WW";

	SADir *pDir = m_saDir;
	SADIter iter;

	int len = 0;
	int nPos = 1;
	while((nPos = tmp.Find('\\', nPos+1)) != -1) 
	{
		strcpy(buf, tmp.Left(nPos).GetBuffer(0) + len );
				
		bool flg = false;
		for( iter = pDir->dirL.begin(); iter != pDir->dirL.end(); iter++ )
		{
			SADir *d = (SADir*)(*iter);
			
			if( strcmp( buf, d->szName ) == 0 )
			{
				pDir = d;
				flg = true;
				break;
			}
		}

		if( flg == false )
		{
			SADir *d = new SADir;
			strcpy( d->szName, buf ); 
			pDir->dirL.push_back( d );
			pDir = d;
			m_nDirCnt++;
			SetDlgItemInt( IDC_STATIC_DIR_CNT, m_nDirCnt, FALSE ); 
		}

		len += strlen(buf) + 1;
	}

	SAFile *file = new SAFile;
	strcpy( file->szName, name );
	file->offset = m_lOffset;
	file->size = length;
	file->time = time;

	pDir->fileL.push_back( file );

}

void SfileDlg::SaveHeader( SADir *dir )
{
	int strSize;
	SAFile *f;
	SADir *d;

	//�����̸� 
	strSize = strlen( dir->szName ) + 1;
	_write( m_fhWH, &strSize, sizeof(int) );
	_write( m_fhWH, dir->szName, strSize );
	

	//���� 
	{
	int size = dir->fileL.size();
	_write( m_fhWH, &size, sizeof(int) );

	SAFIter iter;
	for( iter = dir->fileL.begin(); iter != dir->fileL.end(); iter++ )
	{
		f = (SAFile*)*iter;
		
		//szName
		strSize = strlen( f->szName ) + 1;
		_write( m_fhWH, &strSize, sizeof(int) );
		_write( m_fhWH, f->szName, strSize );
		
		//offset
#ifdef __SAVE_NEWTYPE__
		_write( m_fhWH, &f->offset, sizeof(__int64) );
#else
		unsigned int offset = (unsigned int)f->offset;
		_write( m_fhWH, &offset, sizeof(unsigned long) );
#endif
		//size
		_write( m_fhWH, &f->size, sizeof(unsigned long) );
		//time
		_write( m_fhWH, &f->time, sizeof(long) );
	}
	}

	//���丮
	{
	int size = dir->dirL.size();
	_write( m_fhWH, &size, sizeof(int) );

	SADIter iter;
	for( iter = dir->dirL.begin(); iter != dir->dirL.end(); iter++ )
	{
		d = (SADir*)*iter;
		SaveHeader( d );
	}
	}
}

void SfileDlg::SetControl( BOOL flgEnable)
{
	GetDlgItem(IDC_EDIT_VER)->EnableWindow(flgEnable);
	GetDlgItem(IDC_SPIN_VER)->EnableWindow(flgEnable);
	GetDlgItem(IDC_EDIT_DIR)->EnableWindow(flgEnable);
	GetDlgItem(IDC_EDIT_EXT)->EnableWindow(flgEnable);
	GetDlgItem(IDC_EDIT_TARGET)->EnableWindow(flgEnable);
	GetDlgItem(IDC_BUTTON_FILE_ADD)->EnableWindow(flgEnable);
	GetDlgItem(IDOK)->EnableWindow(flgEnable);
}

void SfileDlg::OnButtonDir() 
{
	// TODO: Add your control notification handler code here
	BROWSEINFO info;
	ITEMIDLIST *item;
	char buf[MAX_PATH];
	memset( &info, 0, sizeof(info) );

	info.hwndOwner = this->m_hWnd;
	info.pszDisplayName = buf; 
	info.lpszTitle = "��������";
	info.ulFlags = BIF_RETURNONLYFSDIRS;

	if( (item = SHBrowseForFolder(&info) ) != NULL )
	{
		SHGetPathFromIDList(item, buf); 
		SetDlgItemText( IDC_EDIT_DIR, buf );
	}

}

void SfileDlg::OnRadio1() 
{
	// TODO: Add your control notification handler code here
	SetDlgItemText( IDC_EDIT_TARGET, "data" );
	
}

void SfileDlg::OnRadio2() 
{
	// TODO: Add your control notification handler code here
	SetDlgItemText( IDC_EDIT_TARGET, "update" );
	
}
