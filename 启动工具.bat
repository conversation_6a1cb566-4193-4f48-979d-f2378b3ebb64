@echo off
title Maintenance Tool

echo ================================================
echo Maintenance Automation Tool
echo ================================================
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.6 or higher
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python environment OK
echo.

echo Checking dependencies...
python -c "import rarfile" >nul 2>&1
if errorlevel 1 (
    echo Missing dependencies, installing...
    python 安装依赖.py
    echo.
)

echo Starting GUI tool...
echo.
python 运维GUI工具.py

if errorlevel 1 (
    echo.
    echo Tool startup failed
    echo Please check error messages and retry
    pause
)
