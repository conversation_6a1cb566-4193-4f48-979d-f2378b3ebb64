@echo off
chcp 65001 >nul
title 运维流程自动化工具

echo ================================================
echo 运维流程自动化工具
echo ================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查依赖包...
python -c "import rarfile" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少依赖包，正在自动安装...
    python 安装依赖.py
    echo.
)

echo 正在启动图形化工具...
echo.
python 运维GUI工具.py

if errorlevel 1 (
    echo.
    echo ❌ 工具启动失败
    echo 请检查错误信息并重试
    pause
)
