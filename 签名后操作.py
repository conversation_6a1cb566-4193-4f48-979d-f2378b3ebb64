#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名后操作脚本
处理签名后的文件操作、MD5生成和配置更新
"""

import os
import shutil
import hashlib
import zipfile
import re
from pathlib import Path

class PostSignatureProcessor:
    def __init__(self):
        self.current_dir = Path.cwd()
        self.patch_dir = self.current_dir / "patch~"
        self.launcher_config = self.current_dir / "launcher.shaiya"
        
    def log(self, message):
        """打印日志信息"""
        print(f"[INFO] {message}")
        
    def error(self, message):
        """打印错误信息"""
        print(f"[ERROR] {message}")
        
    def calculate_md5(self, file_path):
        """计算文件的MD5值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
        
    def process_signed_game_exe(self, signed_game_path):
        """
        处理签名后的game.exe
        1. 将签名后的game(签名).exe放入patch~文件夹
        2. 重命名为game.exe
        """
        self.log("开始处理签名后的game.exe...")
        
        signed_game_path = Path(signed_game_path)
        
        # 检查签名后的文件是否存在
        if not signed_game_path.exists():
            self.error(f"签名后的game.exe不存在: {signed_game_path}")
            return False
            
        # 检查patch~文件夹是否存在
        if not self.patch_dir.exists():
            self.error(f"patch~文件夹不存在: {self.patch_dir}")
            self.error("请先运行签名前操作脚本")
            return False
            
        # 复制签名后的文件到patch~文件夹并重命名
        target_game_path = self.patch_dir / "game.exe"
        shutil.copy2(signed_game_path, target_game_path)
        
        self.log(f"已将签名后的文件复制到: {target_game_path}")
        return True
        
    def create_patch_zip(self):
        """
        创建patch~.zip压缩包
        """
        self.log("创建patch~.zip压缩包...")
        
        if not self.patch_dir.exists():
            self.error(f"patch~文件夹不存在: {self.patch_dir}")
            return None
            
        zip_path = self.current_dir / "patch~.zip"
        
        # 如果zip文件已存在，删除它
        if zip_path.exists():
            zip_path.unlink()
            
        # 创建zip文件
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in self.patch_dir.rglob('*'):
                if file_path.is_file():
                    # 计算相对路径
                    arcname = file_path.relative_to(self.patch_dir)
                    zipf.write(file_path, arcname)
                    self.log(f"添加文件到zip: {arcname}")
                    
        self.log(f"patch~.zip创建完成: {zip_path}")
        return zip_path
        
    def calculate_zip_md5(self, zip_path):
        """计算zip文件的MD5值"""
        self.log("计算patch~.zip的MD5值...")
        md5_value = self.calculate_md5(zip_path)
        self.log(f"MD5值: {md5_value}")
        return md5_value
        
    def update_launcher_config(self, md5_value):
        """
        更新launcher.shaiya配置文件
        1. 修改[Patch]下的Version，使其+1
        2. 追加新的MD5值行
        """
        self.log("更新launcher.shaiya配置文件...")
        
        if not self.launcher_config.exists():
            self.error(f"launcher.shaiya文件不存在: {self.launcher_config}")
            return False
            
        # 读取配置文件
        with open(self.launcher_config, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找当前版本号
        version_pattern = r'Version=0x([0-9a-fA-F]+)'
        version_match = re.search(version_pattern, content)
        
        if not version_match:
            self.error("未找到Version配置项")
            return False
            
        current_version_hex = version_match.group(1)
        current_version_int = int(current_version_hex, 16)
        new_version_int = current_version_int + 1
        new_version_hex = f"{new_version_int:08x}"
        new_version_str = f"0x{new_version_hex}"
        
        self.log(f"当前版本: 0x{current_version_hex}")
        self.log(f"新版本: {new_version_str}")
        
        # 替换版本号
        new_content = re.sub(version_pattern, f'Version={new_version_str}', content)
        
        # 在[Patch]段落末尾添加新的MD5行
        # 查找[Patch]段落的位置
        patch_section_pattern = r'(\[Patch\].*?)(\n\[|\Z)'
        patch_match = re.search(patch_section_pattern, new_content, re.DOTALL)
        
        if patch_match:
            patch_section = patch_match.group(1)
            # 在段落末尾添加新行
            new_md5_line = f"\n{new_version_str}={md5_value}"
            new_patch_section = patch_section + new_md5_line
            new_content = new_content.replace(patch_section, new_patch_section)
        else:
            self.error("未找到[Patch]段落")
            return False
            
        # 写回文件
        with open(self.launcher_config, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        self.log("launcher.shaiya配置文件更新完成")
        self.log(f"添加行: {new_version_str}={md5_value}")
        
        return new_version_str
        
    def rename_zip_to_bin(self, zip_path, md5_value):
        """
        将patch~.zip重命名为MD5值.bin
        """
        self.log("重命名zip文件为.bin文件...")
        
        bin_path = self.current_dir / f"{md5_value}.bin"
        
        # 如果目标文件已存在，删除它
        if bin_path.exists():
            bin_path.unlink()
            
        # 重命名文件
        zip_path.rename(bin_path)
        
        self.log(f"文件已重命名为: {bin_path}")
        return bin_path
        
    def run(self):
        """主运行函数"""
        print("=" * 50)
        print("签名后操作脚本")
        print("=" * 50)
        
        # 获取签名后的game.exe路径
        signed_game_path = input("请输入签名后的game.exe路径 (例如: game(签名).exe): ").strip()
        
        if not signed_game_path:
            self.error("必须提供签名后的game.exe路径")
            return False
            
        # 处理签名后的game.exe
        if not self.process_signed_game_exe(signed_game_path):
            return False
            
        # 创建zip压缩包
        zip_path = self.create_patch_zip()
        if not zip_path:
            return False
            
        # 计算MD5值
        md5_value = self.calculate_zip_md5(zip_path)
        
        # 更新launcher.shaiya配置
        new_version = self.update_launcher_config(md5_value)
        if not new_version:
            return False
            
        # 重命名zip为bin文件
        bin_path = self.rename_zip_to_bin(zip_path, md5_value)
        
        print("\n" + "=" * 50)
        print("签名后操作完成!")
        print(f"新版本号: {new_version}")
        print(f"MD5值: {md5_value}")
        print(f"最终文件: {bin_path}")
        print("=" * 50)
        
        return True

if __name__ == "__main__":
    processor = PostSignatureProcessor()
    try:
        success = processor.run()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n操作被用户取消")
        exit(1)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        exit(1)
