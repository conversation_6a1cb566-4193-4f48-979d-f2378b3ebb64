# 运维流程自动化脚本使用说明

## 概述

根据运维流程文档，我为您创建了两个Python脚本来自动化处理签名前后的操作：

1. **签名前操作.py** - 处理运营包和技术包的准备工作
2. **签名后操作.py** - 处理签名后的文件操作、MD5生成和配置更新

## 文件结构要求

在使用脚本前，请确保以下文件在当前工作目录下：

```
工作目录/
├── 签名前操作.py
├── 签名后操作.py
├── SFile.exe              # 用于打包data文件夹
├── Themida.exe            # 用于game.exe加壳
├── launcher.shaiya        # 配置文件
├── client/                # 客户端文件夹
│   └── data/             # 需要打包的data文件夹
└── game.exe              # 需要加壳的游戏文件
```

## 使用流程

### 第一步：签名前操作

运行签名前操作脚本：

```bash
python 签名前操作.py
```

脚本会提供以下选项：
- **选项1**: 仅处理运营包 (client文件夹)
- **选项2**: 仅处理技术包 (game.exe)
- **选项3**: 处理全部

#### 运营包处理流程：
1. 检查client文件夹下的data文件夹
2. 使用SFile.exe对data文件夹进行打包
3. 生成update.saf和update.sah文件
4. 创建patch~文件夹并将文件移入

#### 技术包处理流程：
1. 使用Themida.exe对game.exe进行加壳
2. 生成game_protected.exe
3. 删除原game.exe并重命名game_protected.exe为game.exe
4. 提示用户将game.exe发送给SP小组

### 第二步：手动操作

**您需要手动完成以下步骤：**
1. 将处理后的game.exe发送给SP小组
2. 等待SP小组返回签名后的文件（通常命名为game(签名).exe）

### 第三步：签名后操作

收到签名后的文件后，运行签名后操作脚本：

```bash
python 签名后操作.py
```

脚本会要求您输入签名后的game.exe文件路径，然后自动完成：

1. 将签名后的文件复制到patch~文件夹并重命名为game.exe
2. 对patch~文件夹进行zip压缩
3. 计算zip文件的MD5值
4. 更新launcher.shaiya配置文件：
   - Version值+1
   - 添加新的MD5值行
5. 将zip文件重命名为MD5值.bin

## 注意事项

### 可能需要您提供支持的地方：

1. **SFile.exe参数调整**
   - 脚本中假设SFile.exe的用法是：`SFile.exe [data文件夹路径]`
   - 如果实际参数不同，需要修改脚本中的subprocess.run调用

2. **Themida.exe参数调整**
   - 脚本中假设Themida.exe的用法是：`Themida.exe [输入文件] [输出文件]`
   - 如果实际参数不同，需要修改脚本中的subprocess.run调用

3. **文件编码问题**
   - launcher.shaiya文件使用UTF-8编码读取
   - 如果文件使用其他编码（如GBK），需要修改脚本中的encoding参数

4. **路径问题**
   - 脚本假设所有工具和文件都在当前工作目录下
   - 如果文件位置不同，需要相应调整路径

### 错误处理

脚本包含了详细的错误检查和日志输出：
- 文件存在性检查
- 工具执行结果验证
- 配置文件格式验证
- 详细的操作日志

如果遇到错误，请查看控制台输出的错误信息，通常会指明具体的问题所在。

## 示例输出

### 签名前操作成功输出：
```
==================================================
签名前操作脚本
==================================================

请选择操作:
1. 处理运营包 (client文件夹)
2. 处理技术包 (game.exe)
3. 处理全部

请输入选择 (1/2/3): 3
请输入client文件夹路径 (默认: ./client): 
请输入game.exe路径 (默认: ./game.exe): 

[INFO] 开始处理运营包...
[INFO] 找到data文件夹: client\data
[INFO] 使用SFile.exe打包data文件夹...
[INFO] SFile.exe执行成功
[INFO] 创建patch~文件夹: patch~
[INFO] 运营包处理完成
[INFO] 开始处理技术包...
[INFO] 使用Themida.exe对game.exe进行加壳...
[INFO] Themida.exe执行成功
[INFO] 删除原game.exe
[INFO] 将game_protected.exe重命名为game.exe
[INFO] 技术包处理完成
[INFO] 请将 game.exe 发送给SP小组进行签名

==================================================
签名前操作完成!
下一步: 将game.exe发送给SP小组进行签名
==================================================
```

### 签名后操作成功输出：
```
==================================================
签名后操作脚本
==================================================

请输入签名后的game.exe路径 (例如: game(签名).exe): game(签名).exe

[INFO] 开始处理签名后的game.exe...
[INFO] 已将签名后的文件复制到: patch~\game.exe
[INFO] 创建patch~.zip压缩包...
[INFO] 添加文件到zip: update.saf
[INFO] 添加文件到zip: update.sah
[INFO] 添加文件到zip: game.exe
[INFO] patch~.zip创建完成: patch~.zip
[INFO] 计算patch~.zip的MD5值...
[INFO] MD5值: 9d3e00816ba0122093fb169b371f17f4
[INFO] 更新launcher.shaiya配置文件...
[INFO] 当前版本: 010000d5
[INFO] 新版本: 0x010000d6
[INFO] launcher.shaiya配置文件更新完成
[INFO] 添加行: 0x010000d6=9d3e00816ba0122093fb169b371f17f4
[INFO] 重命名zip文件为.bin文件...
[INFO] 文件已重命名为: 9d3e00816ba0122093fb169b371f17f4.bin

==================================================
签名后操作完成!
新版本号: 0x010000d6
MD5值: 9d3e00816ba0122093fb169b371f17f4
最终文件: 9d3e00816ba0122093fb169b371f17f4.bin
==================================================
```

## 技术细节

- **Python版本**: 兼容Python 3.6+
- **依赖库**: 仅使用Python标准库，无需额外安装
- **平台兼容性**: Windows/Linux/macOS
- **错误恢复**: 脚本支持中断后重新运行
