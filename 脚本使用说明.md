# 运维流程自动化工具使用说明

## 概述

根据运维流程文档，我为您创建了图形化界面的运维自动化工具：

1. **运维GUI工具.py** - 图形化界面的主工具，包含所有功能
2. **签名前操作.py** - 命令行版本的签名前处理脚本
3. **签名后操作.py** - 命令行版本的签名后处理脚本
4. **安装依赖.py** - 自动安装所需依赖的脚本

## 推荐使用图形化界面工具

**运维GUI工具.py** 提供了更友好的用户界面，包含：
- 可视化文件夹选择
- 自动解压zip/rar文件
- 实时日志输出
- 进度状态显示
- 错误处理和提示

## 快速开始

### 第一步：安装依赖
```bash
python 安装依赖.py
```

### 第二步：启动图形化工具
```bash
python 运维GUI工具.py
```

## 文件结构要求

在使用工具前，请确保以下文件在当前工作目录下：

```
工作目录/
├── 运维GUI工具.py          # 主图形化工具
├── 安装依赖.py            # 依赖安装脚本
├── 签名前操作.py          # 命令行版本（可选）
├── 签名后操作.py          # 命令行版本（可选）
├── SFile.exe              # 用于打包data文件夹
├── Themida.exe            # 用于game.exe加壳
├── launcher.shaiya        # 配置文件
└── 数据文件夹/            # 包含运营和技术文件的文件夹
    ├── 运营包.zip         # 运营相关的压缩包或文件夹
    ├── 技术包.rar         # 技术相关的压缩包或文件夹
    └── ...
```

## 图形化工具使用指南

### 界面说明

1. **数据文件夹选择**：选择包含运营和技术文件的根目录（会自动保存配置）
2. **文件夹浏览区域**：
   - 当前路径显示：显示当前浏览的目录路径
   - 导航按钮：上级目录、刷新、回到根目录
   - 文件夹列表：显示当前目录下的文件夹和压缩包
     - 📁 表示文件夹（双击可进入）
     - 📦 表示压缩包
     - 支持按住Ctrl多选
   - 已选择显示：显示当前选中的文件夹
3. **控制按钮**：
   - 签名前处理：处理选中的文件夹
   - 签名后处理：处理签名后的文件
   - 清空日志：清除日志输出
4. **日志输出区域**：显示详细的处理过程和结果

### 新功能特性

#### 🔍 文件夹浏览
- **双击导航**：双击文件夹图标可以进入该文件夹
- **路径导航**：可以通过"上级目录"按钮返回上层
- **根目录快速返回**：点击"回到根目录"快速返回数据文件夹
- **实时路径显示**：当前路径栏显示您所在的位置

#### 💾 配置自动保存
- **路径记忆**：选择的数据文件夹路径会自动保存
- **下次启动**：重新打开工具时会自动加载上次的数据文件夹
- **配置文件**：配置保存在工作目录的`config.json`文件中

### 使用步骤

#### 第一步：签名前处理

1. 启动图形化工具：`python 运维GUI工具.py`
2. 选择数据文件夹（包含运营和技术文件的目录，会自动保存）
3. 在文件夹浏览区域中导航到包含运营和技术文件的位置：
   - 双击文件夹进入子目录
   - 使用"上级目录"按钮返回上层
   - 使用"回到根目录"快速返回数据文件夹
4. 按住Ctrl键选择需要处理的文件夹和压缩包
5. 点击"签名前处理"按钮
6. 观察日志输出，等待处理完成

**自动处理流程：**
- 自动解压选中的zip/rar文件
- 在解压文件中查找Client/client文件夹
- 运营包：提取data文件夹，使用SFile.exe打包
- 技术包：提取game.exe，使用Themida.exe加壳
- 创建patch~文件夹并整理文件

#### 第二步：手动操作

**您需要手动完成：**
1. 将处理后的game.exe发送给SP小组
2. 等待SP小组返回签名后的文件

#### 第三步：签名后处理

1. 在图形化工具中点击"签名后处理"按钮
2. 选择SP小组返回的签名后game.exe文件
3. 工具自动完成：
   - 将签名文件复制到patch~文件夹
   - 创建zip压缩包
   - 计算MD5值
   - 更新launcher.shaiya配置
   - 生成最终的.bin文件

## 自动化特性

### 智能文件识别
- 自动识别zip和rar压缩文件
- 递归查找Client/client文件夹
- 自动定位data文件夹和game.exe

### 错误处理
- 详细的错误提示和日志
- 文件存在性检查
- 工具可用性验证

### 进度跟踪
- 实时状态显示
- 详细的操作日志
- 成功/失败标识

## 注意事项

### 可能需要您提供支持的地方：

1. **SFile.exe参数调整**
   - 脚本中假设SFile.exe的用法是：`SFile.exe [data文件夹路径]`
   - 如果实际参数不同，需要修改脚本中的subprocess.run调用

2. **Themida.exe参数调整**
   - 脚本中假设Themida.exe的用法是：`Themida.exe [输入文件] [输出文件]`
   - 如果实际参数不同，需要修改脚本中的subprocess.run调用

3. **文件编码问题**
   - launcher.shaiya文件使用UTF-8编码读取
   - 如果文件使用其他编码（如GBK），需要修改脚本中的encoding参数

4. **路径问题**
   - 脚本假设所有工具和文件都在当前工作目录下
   - 如果文件位置不同，需要相应调整路径

### 错误处理

脚本包含了详细的错误检查和日志输出：
- 文件存在性检查
- 工具执行结果验证
- 配置文件格式验证
- 详细的操作日志

如果遇到错误，请查看控制台输出的错误信息，通常会指明具体的问题所在。

## 示例输出

### 签名前操作成功输出：
```
==================================================
签名前操作脚本
==================================================

请选择操作:
1. 处理运营包 (client文件夹)
2. 处理技术包 (game.exe)
3. 处理全部

请输入选择 (1/2/3): 3
请输入client文件夹路径 (默认: ./client): 
请输入game.exe路径 (默认: ./game.exe): 

[INFO] 开始处理运营包...
[INFO] 找到data文件夹: client\data
[INFO] 使用SFile.exe打包data文件夹...
[INFO] SFile.exe执行成功
[INFO] 创建patch~文件夹: patch~
[INFO] 运营包处理完成
[INFO] 开始处理技术包...
[INFO] 使用Themida.exe对game.exe进行加壳...
[INFO] Themida.exe执行成功
[INFO] 删除原game.exe
[INFO] 将game_protected.exe重命名为game.exe
[INFO] 技术包处理完成
[INFO] 请将 game.exe 发送给SP小组进行签名

==================================================
签名前操作完成!
下一步: 将game.exe发送给SP小组进行签名
==================================================
```

### 签名后操作成功输出：
```
==================================================
签名后操作脚本
==================================================

请输入签名后的game.exe路径 (例如: game(签名).exe): game(签名).exe

[INFO] 开始处理签名后的game.exe...
[INFO] 已将签名后的文件复制到: patch~\game.exe
[INFO] 创建patch~.zip压缩包...
[INFO] 添加文件到zip: update.saf
[INFO] 添加文件到zip: update.sah
[INFO] 添加文件到zip: game.exe
[INFO] patch~.zip创建完成: patch~.zip
[INFO] 计算patch~.zip的MD5值...
[INFO] MD5值: 9d3e00816ba0122093fb169b371f17f4
[INFO] 更新launcher.shaiya配置文件...
[INFO] 当前版本: 010000d5
[INFO] 新版本: 0x010000d6
[INFO] launcher.shaiya配置文件更新完成
[INFO] 添加行: 0x010000d6=9d3e00816ba0122093fb169b371f17f4
[INFO] 重命名zip文件为.bin文件...
[INFO] 文件已重命名为: 9d3e00816ba0122093fb169b371f17f4.bin

==================================================
签名后操作完成!
新版本号: 0x010000d6
MD5值: 9d3e00816ba0122093fb169b371f17f4
最终文件: 9d3e00816ba0122093fb169b371f17f4.bin
==================================================
```

## 技术细节

- **Python版本**: 兼容Python 3.6+
- **依赖库**: 仅使用Python标准库，无需额外安装
- **平台兼容性**: Windows/Linux/macOS
- **错误恢复**: 脚本支持中断后重新运行
