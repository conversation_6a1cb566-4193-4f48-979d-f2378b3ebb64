#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维工具测试脚本
用于验证工具的基本功能和环境配置
"""

import os
import sys
from pathlib import Path
import subprocess

def test_python_version():
    """测试Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 6:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.6或更高版本")
        return False

def test_required_modules():
    """测试必需的模块"""
    print("\n🔍 检查必需模块...")
    
    required_modules = [
        'tkinter',
        'zipfile', 
        'hashlib',
        'threading',
        'subprocess',
        'pathlib'
    ]
    
    optional_modules = [
        'rarfile'
    ]
    
    all_good = True
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 缺少必需模块")
            all_good = False
    
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"⚠️  {module} - 可选模块，用于RAR文件支持")
    
    return all_good

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 检查文件结构...")
    
    current_dir = Path.cwd()
    
    required_files = [
        "运维GUI工具.py"
    ]
    
    optional_files = [
        "SFile.exe",
        "Themida.exe", 
        "launcher.shaiya",
        "安装依赖.py",
        "启动工具.bat"
    ]
    
    all_good = True
    
    for file in required_files:
        file_path = current_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 必需文件缺失")
            all_good = False
    
    for file in optional_files:
        file_path = current_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"⚠️  {file} - 可选文件")
    
    return all_good

def test_gui_import():
    """测试GUI模块导入"""
    print("\n🔍 测试GUI模块导入...")
    
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ tkinter GUI模块正常")
        return True
    except Exception as e:
        print(f"❌ tkinter GUI模块错误: {e}")
        return False

def test_tools_execution():
    """测试工具可执行性"""
    print("\n🔍 测试工具可执行性...")
    
    current_dir = Path.cwd()
    tools = [
        ("SFile.exe", "数据打包工具"),
        ("Themida.exe", "游戏加壳工具")
    ]
    
    results = []
    
    for tool, description in tools:
        tool_path = current_dir / tool
        if tool_path.exists():
            try:
                # 尝试运行工具（可能会失败，但至少检查是否可执行）
                result = subprocess.run([str(tool_path)], 
                                      capture_output=True, 
                                      timeout=5)
                print(f"✅ {tool} - {description} 可执行")
                results.append(True)
            except subprocess.TimeoutExpired:
                print(f"✅ {tool} - {description} 可执行（超时正常）")
                results.append(True)
            except Exception as e:
                print(f"⚠️  {tool} - {description} 可能有问题: {e}")
                results.append(False)
        else:
            print(f"⚠️  {tool} - {description} 文件不存在")
            results.append(False)
    
    return all(results) if results else True

def test_config_file():
    """测试配置文件"""
    print("\n🔍 检查配置文件...")
    
    current_dir = Path.cwd()
    config_file = current_dir / "launcher.shaiya"
    
    if not config_file.exists():
        print("⚠️  launcher.shaiya - 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '[Patch]' in content and 'Version=' in content:
            print("✅ launcher.shaiya - 配置文件格式正确")
            return True
        else:
            print("⚠️  launcher.shaiya - 配置文件格式可能有问题")
            return False
            
    except UnicodeDecodeError:
        try:
            with open(config_file, 'r', encoding='gbk') as f:
                content = f.read()
            print("✅ launcher.shaiya - 配置文件（GBK编码）")
            return True
        except Exception as e:
            print(f"❌ launcher.shaiya - 读取失败: {e}")
            return False
    except Exception as e:
        print(f"❌ launcher.shaiya - 读取失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("运维工具环境测试")
    print("=" * 60)
    
    tests = [
        ("Python版本", test_python_version),
        ("必需模块", test_required_modules),
        ("文件结构", test_file_structure),
        ("GUI模块", test_gui_import),
        ("工具可执行性", test_tools_execution),
        ("配置文件", test_config_file)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！工具应该可以正常运行。")
    elif passed >= total * 0.8:
        print("\n⚠️  大部分测试通过，工具基本可用，但可能有些功能受限。")
    else:
        print("\n❌ 多项测试失败，请检查环境配置。")
    
    print("\n建议:")
    print("1. 如果rarfile模块缺失，运行: pip install rarfile")
    print("2. 确保SFile.exe和Themida.exe在当前目录")
    print("3. 确保launcher.shaiya配置文件存在且格式正确")
    print("4. 如有问题，请查看配置示例.md文件")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
