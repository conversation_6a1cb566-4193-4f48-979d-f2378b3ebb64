// SFile.h : main header file for the SFILE application
//

#if !defined(AFX_SFILE_H__2521FABB_B5D7_4400_BC86_8CB599156F8A__INCLUDED_)
#define AFX_SFILE_H__2521FABB_B5D7_4400_BC86_8CB599156F8A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"       // main symbols

/////////////////////////////////////////////////////////////////////////////
// CSFileApp:
// See SFile.cpp for the implementation of this class
//

class CSFileApp : public CWinApp
{
public:
	CSFileApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSFileApp)
	public:
	virtual BOOL InitInstance();
	//}}AFX_VIRTUAL

// Implementation
	//{{AFX_MSG(CSFileApp)
	afx_msg void OnAppAbout();
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

	// 命令行处理函数
	void ProcessMakeCommand(char* dataFolder);
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILE_H__2521FABB_B5D7_4400_BC86_8CB599156F8A__INCLUDED_)
