#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名前操作脚本
根据运维流程自动化处理运营包和技术包的准备工作
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

class PreSignatureProcessor:
    def __init__(self):
        self.current_dir = Path.cwd()
        self.patch_dir = self.current_dir / "patch~"
        
    def log(self, message):
        """打印日志信息"""
        print(f"[INFO] {message}")
        
    def error(self, message):
        """打印错误信息"""
        print(f"[ERROR] {message}")
        
    def process_client_data(self, client_path):
        """
        处理运营包 - 客户端文件
        1. 检查client文件夹下的data文件夹
        2. 使用SFile.exe打包
        3. 创建patch~文件夹并移动生成的文件
        """
        self.log("开始处理运营包...")
        
        client_path = Path(client_path)
        data_path = client_path / "data"
        
        # 检查data文件夹是否存在
        if not data_path.exists():
            self.error(f"data文件夹不存在: {data_path}")
            return False
            
        self.log(f"找到data文件夹: {data_path}")
        
        # 检查SFile.exe是否存在
        sfile_exe = self.current_dir / "SFile.exe"
        if not sfile_exe.exists():
            self.error(f"SFile.exe不存在: {sfile_exe}")
            self.error("请确保SFile.exe在当前目录下")
            return False
            
        # 使用SFile.exe打包data文件夹
        self.log("使用SFile.exe打包data文件夹...")
        try:
            # 注意：这里需要根据SFile.exe的实际参数调整命令
            # 假设SFile.exe的用法是: SFile.exe [data文件夹路径]
            result = subprocess.run([str(sfile_exe), str(data_path)], 
                                  capture_output=True, text=True, cwd=self.current_dir)
            
            if result.returncode != 0:
                self.error(f"SFile.exe执行失败: {result.stderr}")
                return False
                
            self.log("SFile.exe执行成功")
            
        except Exception as e:
            self.error(f"执行SFile.exe时出错: {e}")
            return False
            
        # 检查生成的文件
        update_saf = self.current_dir / "update.saf"
        update_sah = self.current_dir / "update.sah"
        
        if not update_saf.exists() or not update_sah.exists():
            self.error("未找到生成的update.saf或update.sah文件")
            return False
            
        # 创建patch~文件夹
        if self.patch_dir.exists():
            self.log("patch~文件夹已存在，将清空内容")
            shutil.rmtree(self.patch_dir)
            
        self.patch_dir.mkdir()
        self.log(f"创建patch~文件夹: {self.patch_dir}")
        
        # 移动文件到patch~文件夹
        shutil.move(str(update_saf), str(self.patch_dir / "update.saf"))
        shutil.move(str(update_sah), str(self.patch_dir / "update.sah"))
        
        self.log("运营包处理完成")
        return True
        
    def process_game_exe(self, game_exe_path):
        """
        处理技术包 - game.exe
        1. 使用Themida.exe对game.exe进行加壳
        2. 生成game_protected.exe
        3. 删除原game.exe并重命名
        """
        self.log("开始处理技术包...")
        
        game_exe_path = Path(game_exe_path)
        
        # 检查game.exe是否存在
        if not game_exe_path.exists():
            self.error(f"game.exe不存在: {game_exe_path}")
            return False
            
        # 检查Themida.exe是否存在
        themida_exe = self.current_dir / "Themida.exe"
        if not themida_exe.exists():
            self.error(f"Themida.exe不存在: {themida_exe}")
            self.error("请确保Themida.exe在当前目录下")
            return False
            
        # 使用Themida.exe加壳
        self.log("使用Themida.exe对game.exe进行加壳...")
        try:
            # 注意：这里需要根据Themida.exe的实际参数调整命令
            # 假设Themida.exe的用法是: Themida.exe [输入文件] [输出文件]
            game_protected_path = game_exe_path.parent / "game_protected.exe"
            
            result = subprocess.run([str(themida_exe), str(game_exe_path), str(game_protected_path)], 
                                  capture_output=True, text=True, cwd=self.current_dir)
            
            if result.returncode != 0:
                self.error(f"Themida.exe执行失败: {result.stderr}")
                return False
                
            self.log("Themida.exe执行成功")
            
        except Exception as e:
            self.error(f"执行Themida.exe时出错: {e}")
            return False
            
        # 检查生成的game_protected.exe
        game_protected_path = game_exe_path.parent / "game_protected.exe"
        if not game_protected_path.exists():
            self.error("未找到生成的game_protected.exe文件")
            return False
            
        # 删除原game.exe
        game_exe_path.unlink()
        self.log("删除原game.exe")
        
        # 重命名game_protected.exe为game.exe
        game_protected_path.rename(game_exe_path)
        self.log("将game_protected.exe重命名为game.exe")
        
        self.log("技术包处理完成")
        self.log(f"请将 {game_exe_path} 发送给SP小组进行签名")
        
        return True
        
    def run(self):
        """主运行函数"""
        print("=" * 50)
        print("签名前操作脚本")
        print("=" * 50)
        
        # 获取用户输入
        print("\n请选择操作:")
        print("1. 处理运营包 (client文件夹)")
        print("2. 处理技术包 (game.exe)")
        print("3. 处理全部")
        
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice in ["1", "3"]:
            client_path = input("请输入client文件夹路径 (默认: ./client): ").strip()
            if not client_path:
                client_path = "./client"
                
            if not self.process_client_data(client_path):
                self.error("运营包处理失败")
                return False
                
        if choice in ["2", "3"]:
            game_exe_path = input("请输入game.exe路径 (默认: ./game.exe): ").strip()
            if not game_exe_path:
                game_exe_path = "./game.exe"
                
            if not self.process_game_exe(game_exe_path):
                self.error("技术包处理失败")
                return False
                
        print("\n" + "=" * 50)
        print("签名前操作完成!")
        print("下一步: 将game.exe发送给SP小组进行签名")
        print("=" * 50)
        
        return True

if __name__ == "__main__":
    processor = PreSignatureProcessor()
    try:
        success = processor.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        sys.exit(1)
