// MainFrm.h : interface of the CMainFrame class
//
/////////////////////////////////////////////////////////////////////////////

#if !defined(AFX_MAINFRM_H__09B85321_1682_4630_8561_FC22576E8BE7__INCLUDED_)
#define AFX_MAINFRM_H__09B85321_1682_4630_8561_FC22576E8BE7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class SfileUpdateDlg;
class SfileExtractDlg;
class SfileDeleteListDlg;

class CMainFrame : public CFrameWnd
{
	
protected: // create from serialization only
	CMainFrame();
	DECLARE_DYNCREATE(CMainFrame)

// Attributes
public:

// Operations
public:
	SfileExtractDlg *m_extractDlg;
	SfileDeleteListDlg *m_deleteListDlg;
	SfileUpdateDlg	*m_updateDlg;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CMainFrame)
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	//}}AFX_VIRTUAL

// Implementation
public:
	void OpenExtractView( char *fileName );
	void OpenUpdateView( char *fileName );
	void OpenUpdateViewAuto( char *dataFolder, char *sahFile );

	virtual ~CMainFrame();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:  // control bar embedded members
	CStatusBar  m_wndStatusBar;
	CToolBar    m_wndToolBar;

// Generated message map functions
protected:
	//{{AFX_MSG(CMainFrame)
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSfileAdd();
	afx_msg void OnSfileMerge();
	afx_msg void OnSfileExtract();
	afx_msg void OnSfileDeletelist();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_MAINFRM_H__09B85321_1682_4630_8561_FC22576E8BE7__INCLUDED_)
