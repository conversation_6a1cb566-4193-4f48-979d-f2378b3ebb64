#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SFile路径修复
"""

import subprocess
import sys
import os
from pathlib import Path
import shutil

def create_simple_test():
    """创建简单的测试数据"""
    # 创建英文路径测试
    test_dir = Path.cwd() / "simple_test"
    
    if test_dir.exists():
        shutil.rmtree(test_dir)
    
    test_dir.mkdir()
    
    # 创建测试文件
    (test_dir / "test1.txt").write_text("Hello World 1")
    (test_dir / "test2.dat").write_text("Hello World 2")
    
    # 创建子文件夹
    sub_dir = test_dir / "subfolder"
    sub_dir.mkdir()
    (sub_dir / "sub_test.txt").write_text("Sub file content")
    
    print(f"✅ 创建测试数据: {test_dir}")
    return test_dir

def test_sfile_fixed():
    """测试修复后的SFile"""
    print("=" * 60)
    print("验证SFile路径修复")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_simple_test()
    
    try:
        # 检查SFile.exe
        current_dir = Path.cwd()
        sfile_exe = current_dir / "SFile.exe"
        sfile_new = current_dir / "SFile_new.exe"
        
        if sfile_new.exists():
            sfile_path = sfile_new
            print(f"🔧 使用修复版: {sfile_path}")
        elif sfile_exe.exists():
            sfile_path = sfile_exe
            print(f"📦 使用原版: {sfile_path}")
        else:
            print("❌ 未找到SFile.exe")
            return False
        
        # 清理旧文件
        for old_file in ["update.saf", "update.sah"]:
            old_path = current_dir / old_file
            if old_path.exists():
                old_path.unlink()
        
        # 测试命令
        print(f"\n🧪 测试路径: {test_dir}")
        print(f"绝对路径: {test_dir.resolve()}")
        
        cmd = [str(sfile_path), "-make", str(test_dir.resolve())]
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=current_dir,
            timeout=30
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print(f"输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"错误:\n{result.stderr}")
        
        # 检查结果
        import time
        time.sleep(1)
        
        print("\n📁 检查生成的文件:")
        
        update_saf = current_dir / "update.saf"
        update_sah = current_dir / "update.sah"
        
        success = True
        
        if update_saf.exists():
            size = update_saf.stat().st_size
            print(f"✅ update.saf - {size} bytes")
        else:
            print("❌ update.saf - 未生成")
            success = False
        
        if update_sah.exists():
            size = update_sah.stat().st_size
            print(f"✅ update.sah - {size} bytes")
        else:
            print("❌ update.sah - 未生成")
            success = False
        
        # 总结
        print("\n" + "=" * 60)
        if success:
            print("🎉 路径修复验证成功!")
            print("✅ 不再出现问号错误")
            print("✅ 生成了完整的文件")
        else:
            print("❌ 路径修复验证失败")
            print("可能需要:")
            print("1. 重新编译SFile.exe")
            print("2. 检查修复是否正确应用")
        print("=" * 60)
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return False
    finally:
        # 清理
        try:
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"\n🧹 已清理测试数据")
        except:
            pass

def main():
    """主函数"""
    print("SFile路径问题修复验证")
    
    # 先运行调试脚本分析问题
    print("\n📊 运行路径计算分析...")
    try:
        subprocess.run([sys.executable, "调试TARGETDIR问题.py"], timeout=10)
    except:
        pass
    
    # 测试修复效果
    success = test_sfile_fixed()
    
    if success:
        print("\n✅ 修复成功!")
        print("现在可以正常使用SFile.exe命令行功能了")
    else:
        print("\n❌ 修复可能不完整")
        print("建议:")
        print("1. 运行 '编译SFile.bat' 重新编译")
        print("2. 确保所有修改都已应用")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
