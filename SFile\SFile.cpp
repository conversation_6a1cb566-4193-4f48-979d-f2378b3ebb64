// SFile.cpp : Defines the class behaviors for the application.
//

#include "stdafx.h"
#include "SFile.h"

#include "MainFrm.h"
#include "SFileDoc.h"
#include "SFileView.h"
#include "SfileDlg.h"
#include "SAFile.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSFileApp

BEGIN_MESSAGE_MAP(CSFileApp, CWinApp)
	//{{AFX_MSG_MAP(CSFileApp)
	ON_COMMAND(ID_APP_ABOUT, OnAppAbout)
		// NOTE - the ClassWizard will add and remove mapping macros here.
		//    DO NOT EDIT what you see in these blocks of generated code!
	//}}AFX_MSG_MAP
	// Standard file based document commands
	ON_COMMAND(ID_FILE_NEW, CWinApp::OnFileNew)
	ON_COMMAND(ID_FILE_OPEN, CWinApp::OnFileOpen)
	// Standard print setup command
	ON_COMMAND(ID_FILE_PRINT_SETUP, CWinApp::OnFilePrintSetup)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSFileApp construction

CSFileApp::CSFileApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
}

/////////////////////////////////////////////////////////////////////////////
// The one and only CSFileApp object

CSFileApp theApp;

/////////////////////////////////////////////////////////////////////////////
// CSFileApp initialization

BOOL CSFileApp::InitInstance()
{
	AfxEnableControlContainer();

	// Standard initialization
	// If you are not using these features and wish to reduce the size
	//  of your final executable, you should remove from the following
	//  the specific initialization routines you do not need.

#ifdef _AFXDLL
	Enable3dControls();			// Call this when using MFC in a shared DLL
#else
	Enable3dControlsStatic();	// Call this when linking to MFC statically
#endif

	// Change the registry key under which our settings are stored.
	// TODO: You should modify this string to be something appropriate
	// such as the name of your company or organization.
	SetRegistryKey(_T("Local AppWizard-Generated Applications"));

	LoadStdProfileSettings();  // Load standard INI file options (including MRU)

	// Register the application's document templates.  Document templates
	//  serve as the connection between documents, frame windows and views.

	CSingleDocTemplate* pDocTemplate;
	pDocTemplate = new CSingleDocTemplate(
		IDR_MAINFRAME,
		RUNTIME_CLASS(CSFileDoc),
		RUNTIME_CLASS(CMainFrame),       // main SDI frame window
		RUNTIME_CLASS(CSFileView));
	AddDocTemplate(pDocTemplate);

	// Parse command line for standard shell commands, DDE, file open
	CCommandLineInfo cmdInfo;
	ParseCommandLine(cmdInfo);

	// Dispatch commands specified on the command line
	if (!ProcessShellCommand(cmdInfo))
		return FALSE;

	// The one and only window has been initialized, so show and update it.
	m_pMainWnd->ShowWindow(SW_SHOW);
	m_pMainWnd->UpdateWindow();

	// 命令行参数处理
	// 检查命令行参数
	CString cmdLine = GetCommandLine();
	cmdLine.MakeLower();

	// 支持的命令行格式:
	// SFile.exe -make "data_folder_path"
	// SFile.exe -extract "sah_file_path"

	if( cmdLine.Find("-make") != -1 )
	{
		// Make模式 - 创建SAF/SAH文件
		int argc = 0;
		LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);

		if( argc >= 3 )  // 程序名 + -make + data_folder
		{
			// 转换为ANSI字符串
			char dataFolder[MAX_PATH];
			WideCharToMultiByte(CP_ACP, 0, argv[2], -1, dataFolder, MAX_PATH, NULL, NULL);

			// 调用命令行处理函数
			ProcessMakeCommand(dataFolder);
		}
		else
		{
			MessageBox(NULL, "Usage: SFile.exe -make \"data_folder_path\"", "Error", MB_OK);
		}

		LocalFree(argv);
		return TRUE;  // 处理完成后退出
	}
	else if( cmdLine.Find("-extract") != -1 )
	{
		// Extract模式
		if( cmdInfo.m_nShellCommand == CCommandLineInfo::FileOpen )
		{
			CMainFrame *frm = (CMainFrame*)m_pMainWnd;
			frm->OpenExtractView( cmdInfo.m_strFileName.GetBuffer(0) );
		}
	}
	else if( cmdInfo.m_nShellCommand == CCommandLineInfo::FileOpen )
	{
		// 默认为Extract模式（向后兼容）
		CMainFrame *frm = (CMainFrame*)m_pMainWnd;
		frm->OpenExtractView( cmdInfo.m_strFileName.GetBuffer(0) );
	}

	return TRUE;
}

// 命令行处理函数
void CSFileApp::ProcessMakeCommand(char* dataFolder)
{
	// 设置全局变量（从SfileDlg.cpp中的全局变量）
	extern char FILEEXT[1024];
	extern char TARGETDIR[2048];
	extern char OUTFILEPATH[2048];
	extern char OUTFILENAME[1024];
	extern char OUTFILEHEADERNAME[1024];

	// 检查路径是否包含中文字符
	bool hasChineseChars = false;
	for (int i = 0; dataFolder[i] != '\0'; i++) {
		if ((unsigned char)dataFolder[i] > 127) {
			hasChineseChars = true;
			break;
		}
	}

	char processPath[2048];

	if (hasChineseChars) {
		// 如果包含中文，尝试获取短路径名
		char shortPath[2048];
		DWORD result = GetShortPathNameA(dataFolder, shortPath, sizeof(shortPath));

		if (result > 0 && result < sizeof(shortPath)) {
			strcpy(processPath, shortPath);
			OutputDebugStringA("Using short path for Chinese characters");
		} else {
			// 如果获取短路径失败，显示错误并退出
			MessageBoxA(NULL,
				"路径包含中文字符且无法转换为短路径。\n请将文件移动到英文路径下，例如:\nC:\\GameData\\data",
				"路径错误",
				MB_OK | MB_ICONERROR);
			return;
		}
	} else {
		strcpy(processPath, dataFolder);
	}

	// 设置参数
	strcpy(FILEEXT, "*.*");  // 所有文件
	strcpy(TARGETDIR, processPath);  // 目标目录

	// 确保目录路径以反斜杠结尾
	int len = strlen(TARGETDIR);
	if (len > 0 && TARGETDIR[len-1] != '\\') {
		strcat(TARGETDIR, "\\");
	}

	// 设置输出文件路径（当前目录）
	GetCurrentDirectoryA(2048, OUTFILEPATH);
	strcat(OUTFILEPATH, "\\");

	// 设置输出文件名
	strcpy(OUTFILENAME, "update");
	strcpy(OUTFILEHEADERNAME, "update");

	// 输出调试信息
	char debugMsg[4096];
	sprintf(debugMsg, "Processing:\nOriginal: %s\nTarget Dir: %s\nOutput Path: %s\nFile Ext: %s\nOutput Name: %s",
		dataFolder, TARGETDIR, OUTFILEPATH, FILEEXT, OUTFILENAME);
	OutputDebugStringA(debugMsg);

	// 创建并初始化SfileDlg对象
	SfileDlg* dlg = new SfileDlg();

	// 手动初始化必要的成员变量
	dlg->m_saDir = new SADir;
	memset(dlg->m_saDir->szName, 0, sizeof(dlg->m_saDir->szName));
	dlg->m_fhW = -1;
	dlg->m_fhWH = -1;
	dlg->m_nFileCnt = 0;
	dlg->m_nDirCnt = 1;
	dlg->m_nFileSize = 0;
	dlg->m_lOffset = 0;
	dlg->m_nVer = 1;  // 版本号

	// 打开输出文件
	CString strFileName;
	strFileName = strFileName + OUTFILEPATH + OUTFILENAME;
	dlg->m_fhW = _open(strFileName.GetBuffer(0), _O_WRONLY | _O_TRUNC | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE);

	if (dlg->m_fhW == -1) {
		MessageBoxA(NULL, "无法创建输出文件 update.saf", "错误", MB_OK | MB_ICONERROR);
		delete dlg->m_saDir;
		delete dlg;
		return;
	}

	strFileName.Empty();
	strFileName = strFileName + OUTFILEPATH + OUTFILEHEADERNAME;
	dlg->m_fhWH = _open(strFileName.GetBuffer(0), _O_WRONLY | _O_TRUNC | _O_CREAT | _O_BINARY, _S_IREAD | _S_IWRITE);

	if (dlg->m_fhWH == -1) {
		MessageBoxA(NULL, "无法创建输出文件 update.sah", "错误", MB_OK | MB_ICONERROR);
		_close(dlg->m_fhW);
		delete dlg->m_saDir;
		delete dlg;
		return;
	}

	// 调用ThreadMakeDataFile函数
	DWORD result = ThreadMakeDataFile(dlg);

	// 清理资源
	if (dlg->m_fhW != -1) {
		_close(dlg->m_fhW);
	}
	if (dlg->m_fhWH != -1) {
		_close(dlg->m_fhWH);
	}

	// 清理SADir结构
	if (dlg->m_saDir) {
		// 这里应该调用DeleteFileList，但为了简化，我们直接delete
		delete dlg->m_saDir;
	}

	delete dlg;

	if (result == 0) {
		OutputDebugStringA("SFile processing completed successfully");
		MessageBoxA(NULL, "处理完成！已生成update.saf和update.sah文件。", "成功", MB_OK | MB_ICONINFORMATION);
	} else {
		OutputDebugStringA("SFile processing failed");
		MessageBoxA(NULL, "处理失败！请检查路径和文件权限。", "错误", MB_OK | MB_ICONERROR);
	}
}


/////////////////////////////////////////////////////////////////////////////
// CAboutDlg dialog used for App About

class CAboutDlg : public CDialog
{
public:
	CAboutDlg();

// Dialog Data
	//{{AFX_DATA(CAboutDlg)
	enum { IDD = IDD_ABOUTBOX };
	//}}AFX_DATA

	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAboutDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	//{{AFX_MSG(CAboutDlg)
		// No message handlers
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialog(CAboutDlg::IDD)
{
	//{{AFX_DATA_INIT(CAboutDlg)
	//}}AFX_DATA_INIT
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAboutDlg)
	//}}AFX_DATA_MAP
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialog)
	//{{AFX_MSG_MAP(CAboutDlg)
		// No message handlers
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

// App command to run the dialog
void CSFileApp::OnAppAbout()
{
	CAboutDlg aboutDlg;
	aboutDlg.DoModal();
}

/////////////////////////////////////////////////////////////////////////////
// CSFileApp message handlers

