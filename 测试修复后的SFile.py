#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的SFile.exe
"""

import subprocess
import sys
import os
from pathlib import Path
import tempfile
import shutil

def create_test_data():
    """创建测试数据"""
    # 在当前目录创建测试数据
    test_dir = Path.cwd() / "test_data_simple"
    
    if test_dir.exists():
        shutil.rmtree(test_dir)
    
    test_dir.mkdir()
    
    # 创建一些测试文件
    files = [
        ("test1.txt", "This is test file 1\nContent for testing SFile.exe"),
        ("test2.dat", "Binary test data\x00\x01\x02\x03"),
        ("config.ini", "[Settings]\nVersion=1.0\nDebug=true"),
        ("readme.md", "# Test Data\nThis is test data for SFile.exe")
    ]
    
    for filename, content in files:
        file_path = test_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"✅ 创建测试数据: {test_dir}")
    print(f"   包含 {len(files)} 个测试文件")
    
    return test_dir

def test_sfile_command():
    """测试SFile命令行"""
    print("=" * 60)
    print("测试修复后的SFile.exe")
    print("=" * 60)
    
    # 创建测试数据
    test_data_dir = create_test_data()
    
    try:
        # 检查SFile.exe
        current_dir = Path.cwd()
        sfile_exe = current_dir / "SFile.exe"
        sfile_new = current_dir / "SFile_new.exe"
        
        if sfile_new.exists():
            sfile_path = sfile_new
            print(f"🔧 使用修复版: {sfile_path}")
        elif sfile_exe.exists():
            sfile_path = sfile_exe
            print(f"📦 使用原版: {sfile_path}")
        else:
            print("❌ 未找到SFile.exe")
            return False
        
        # 清理之前的输出文件
        for old_file in ["update.saf", "update.sah"]:
            old_path = current_dir / old_file
            if old_path.exists():
                old_path.unlink()
                print(f"🧹 清理旧文件: {old_file}")
        
        # 测试命令行
        print(f"\n🧪 测试路径: {test_data_dir}")
        
        cmd = [str(sfile_path), "-make", str(test_data_dir)]
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=current_dir,
            timeout=60
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"错误输出:\n{result.stderr}")
        
        # 等待文件生成
        import time
        time.sleep(2)
        
        # 检查生成的文件
        print("\n📁 检查生成的文件:")
        
        update_saf = current_dir / "update.saf"
        update_sah = current_dir / "update.sah"
        
        success_count = 0
        
        if update_saf.exists():
            size = update_saf.stat().st_size
            print(f"✅ update.saf - {size} bytes")
            success_count += 1
        else:
            print("❌ update.saf - 未生成")
        
        if update_sah.exists():
            size = update_sah.stat().st_size
            print(f"✅ update.sah - {size} bytes")
            success_count += 1
        else:
            print("❌ update.sah - 未生成")
        
        # 检查其他可能的输出文件
        print("\n🔍 检查其他输出文件:")
        found_other = False
        for pattern in ["*.saf", "*.sah"]:
            for file in current_dir.glob(pattern):
                if file.name not in ["update.saf", "update.sah"]:
                    size = file.stat().st_size
                    print(f"📦 {file.name} - {size} bytes")
                    found_other = True
        
        if not found_other:
            print("   无其他输出文件")
        
        # 总结结果
        print("\n" + "=" * 60)
        print("测试结果")
        print("=" * 60)
        
        if success_count == 2:
            print("🎉 测试成功!")
            print("✅ 生成了完整的.saf和.sah文件")
            print("SFile.exe命令行功能正常工作")
            return True
        elif success_count == 1:
            print("⚠️  部分成功")
            print("只生成了部分文件，可能需要进一步调试")
            return False
        else:
            print("❌ 测试失败")
            print("未生成任何输出文件")
            print("\n可能的原因:")
            print("1. SFile.exe初始化失败")
            print("2. 文件权限问题")
            print("3. 路径处理错误")
            print("4. 需要重新编译SFile.exe")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return False
    finally:
        # 清理测试数据
        try:
            if test_data_dir.exists():
                shutil.rmtree(test_data_dir)
                print(f"\n🧹 已清理测试数据: {test_data_dir}")
        except:
            pass

def main():
    """主函数"""
    print("SFile.exe 修复验证工具")
    
    success = test_sfile_command()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 修复验证成功!")
        print("SFile.exe现在可以正常工作了")
        print("可以在运维工具中使用命令行功能")
    else:
        print("❌ 修复验证失败")
        print("建议:")
        print("1. 重新编译SFile.exe: 运行 '编译SFile.bat'")
        print("2. 检查源码修改是否正确")
        print("3. 确保所有必要的头文件都包含了")
        print("4. 检查SADir和相关结构的定义")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
